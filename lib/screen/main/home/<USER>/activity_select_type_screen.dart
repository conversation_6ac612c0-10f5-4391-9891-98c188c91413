import 'package:auto_route/auto_route.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/local/local_storage.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/chat/chat_context.dart';
import 'package:family_app/screen/main/chat/chat_parameter.dart';
import 'package:family_app/screen/main/home/<USER>/activity_select_type_cubit.dart';
import 'package:family_app/screen/main/home/<USER>/activity_select_type_state.dart';
import 'package:family_app/utils/loading.dart';
import 'package:family_app/widget/appbar_custom.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

@RoutePage()
class ActivitySelectTypePage extends BaseBlocProvider<ActivitySelectTypeState, ActivitySelectTypeCubit> {
  const ActivitySelectTypePage({super.key});

  @override
  Widget buildPage() => const ActivitySelectTypeView();

  @override
  ActivitySelectTypeCubit createCubit() => ActivitySelectTypeCubit(activityRepository: locator.get());
}

class ActivitySelectTypeView extends StatefulWidget {
  const ActivitySelectTypeView({super.key});

  @override
  State<ActivitySelectTypeView> createState() => _ActivitySelectTypeViewState();
}

class _ActivitySelectTypeViewState
    extends BaseBlocPageState<ActivitySelectTypeView, ActivitySelectTypeState, ActivitySelectTypeCubit> {
  @override
  bool? get isBottomSafeArea => false;

  @override
  Color get backgroundColor => appTheme.lightGreyColor;

  @override
  bool listenWhen(ActivitySelectTypeState previous, ActivitySelectTypeState current) {
    if (current.loading) {
      showLoading();
    } else {
      dismissLoading();
    }
    return super.listenWhen(previous, current);
  }

  @override
  Widget buildAppBar(BuildContext context, ActivitySelectTypeCubit cubit, ActivitySelectTypeState state) {
    return CustomAppBar2(
      title: 'New activity',
      showBack: true,
      actions: [],
    );
  }

  @override
  Widget buildBody(BuildContext context, ActivitySelectTypeCubit cubit, ActivitySelectTypeState state) {
    return _buildEmptyView(cubit, state);
  }

  Widget _buildEmptyView(ActivitySelectTypeCubit cubit, ActivitySelectTypeState state) {
    return Padding(
      padding: padding(horizontal: 5, vertical: 2),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _buildNewTrip(cubit, state),
          const SizedBox(height: 16),
          _buildNewBirthday(cubit, state),
        ],
      ),
    );
  }

  Widget _buildNewTrip(ActivitySelectTypeCubit cubit, ActivitySelectTypeState state) {
    return _buildNewActivity(cubit, state, ActivityType.trip);
  }

  Widget _buildNewBirthday(ActivitySelectTypeCubit cubit, ActivitySelectTypeState state) {
    return _buildNewActivity(cubit, state, ActivityType.birthdayRegistry);
  }

  Widget _buildNewActivity(ActivitySelectTypeCubit cubit, ActivitySelectTypeState state, ActivityType activityType) {
    final isTrip = activityType == ActivityType.trip;
    final text = isTrip ? 'New trip' : 'New birthday registry';
    final desc = isTrip
        ? 'Create memories with your family by planning the traveling together!'
        : 'Plan a delightful surprise for your loved one\'s birthday!';
    final iconPath = isTrip ? Assets.icons.icBeachChair.path : Assets.icons.icBirthday.path;
    const textColor = Colors.black;
    const descTextColor = Color(0xFF595D62);
    final boxColor = isTrip ? const Color(0x1F3448F0) : const Color(0x1F2E7D32);
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16), // Add padding
      // Add Material widget to make InkWell have ripple effect
      child: Material(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          child: InkWell(
              onTap: () {
                cubit.updateActivityType(activityType);
                goToNewActivity(activityType);
              },
              borderRadius: BorderRadius.circular(20),
              child: Container(
                padding: const EdgeInsets.all(16), // Adjust padding as needed
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(width: 3.w, color: boxColor), // Set border color to boxColor
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.max, // Make Row take only needed space
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    SvgPicture.asset(
                      iconPath,
                      width: 50, // Adjust icon size as needed
                      height: 50, // Adjust icon size as needed
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            text,
                            style: const TextStyle(
                              color: textColor, // Text color
                              fontSize: 16, // Adjust font size as needed
                              fontWeight: FontWeight.bold, // Bold font weight
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            desc,
                            style: const TextStyle(
                              color: descTextColor, // Lighter text color
                              fontSize: 14, // Adjust font size as needed
                            ),
                            maxLines: 3, // Ensure text is displayed on multiple lines if too long
                            overflow: TextOverflow.visible, // Add ellipsis if text overflows
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ))),
    );
  }

  void goToNewActivity(ActivityType activityType) async {
    LocalStorage localStorage = locator.get();
    final token = await localStorage.accessToken();
    if (token!.isEmpty) {
      await localStorage.clear();
      context.replaceRoute(const AuthRoute());
    } else {
      final result = (activityType == ActivityType.trip)
          ? context
              .pushRoute(ChatRoute(parameter: ChatParameter(chatContext: ChatContext.getGeneralChatContext(token))))
          : context.pushRoute(const BirthdayRegistryUpsertRoute());
      result.then((value) {
        if (value != null) {
          Navigator.of(context).pop(value);
        }
      });
    }
  }
}
