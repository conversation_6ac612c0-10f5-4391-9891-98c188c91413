import 'package:auto_route/auto_route.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/memory_model.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/screen/main/memories/memory_upsert_screen.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:family_app/widget/appbar_custom.dart';
import 'package:family_app/widget/circle_item.dart';
import 'package:family_app/widget/expandable_caption.dart';
import 'package:family_app/widget/image_asset_custom.dart';
import 'package:family_app/widget/image_viewer.dart/image_viewer_pager.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:flutter_svg/svg.dart';

import 'memories_cubit.dart';
import 'memories_state.dart';
import 'widget/memories_activity_info.dart';

@RoutePage()
class MemoriesPage extends BaseBlocProvider<MemoriesState, MemoriesCubit> {
  const MemoriesPage({super.key});

  @override
  Widget buildPage() => const MemoriesScreen();

  @override
  MemoriesCubit createCubit() => MemoriesCubit(
        familyRepository: locator.get(),
        uploadRepository: locator.get(),
        activityRepository: locator.get(),
      );
}

class MemoriesScreen extends StatefulWidget {
  const MemoriesScreen({super.key});

  @override
  State<MemoriesScreen> createState() => _MemoriesScreenState();
}

class _MemoriesScreenState extends BaseBlocPageState<MemoriesScreen, MemoriesState, MemoriesCubit> {
  final GlobalKey<RefreshIndicatorState> _refreshIndicatorKey = GlobalKey<RefreshIndicatorState>();

  @override
  bool listenWhen(MemoriesState previous, MemoriesState current) {
    if (current.status == MemoriesStatus.loading) {
      _refreshIndicatorKey.currentState?.show();
    } else if (current.status == MemoriesStatus.error) {
      _refreshIndicatorKey.currentState?.deactivate();
      showSimpleToast(current.errorMessage ?? 'Failed to load memories');
    } else if (current.status == MemoriesStatus.success) {
      _refreshIndicatorKey.currentState?.deactivate();
    }
    return super.listenWhen(previous, current);
  }

  @override
  Widget buildAppBar(BuildContext context, MemoriesCubit c, MemoriesState __) {
    return CustomAppBar2(
      title: LocaleKeys.memories.tr(),
      showBack: true,
      actions: [
        GestureDetector(
          onTap: () async {
            MemoryUpsertBts.show(context).then(
              (result) {
                if (result == true) {
                  c.fetchMemories();
                }
              },
            );
          },
          behavior: HitTestBehavior.opaque,
          child: CircleItem(
            backgroundColor: appTheme.blackColor.withOpacity(.05),
            padding: padding(all: 7),
            child: SvgPicture.asset(Assets.icons.icPhoto.path),
          ),
        )
      ],
    );
  }

  @override
  Widget buildBody(BuildContext context, MemoriesCubit cubit, MemoriesState state) {
    AppLogger.d("Build Body.. state.status: ${cubit.state.status}");

    return BlocBuilder<MemoriesCubit, MemoriesState>(
      builder: (context, state) {
        if (state.status == MemoriesStatus.loading) {
          return const Center(child: CircularProgressIndicator());
        } else if (state.status == MemoriesStatus.success) {
          return RefreshIndicator(
            key: _refreshIndicatorKey,
            onRefresh: cubit.fetchMemories,
            child: _buildMemoryView(cubit, state),
          );
        } else if (state.status == MemoriesStatus.error) {
          return Center(child: Text(state.errorMessage!));
        } else {
          return Container(); // Or show something else in the initial state
        }
      },
    );
  }

  Widget _buildMemoryView(MemoriesCubit cubit, MemoriesState state) {
    if (state.memories.isEmpty) {
      return _buildEmptyView(cubit);
    } else {
      return _buildMemoryList(cubit, state);
    }
  }

  Widget _buildEmptyView(MemoriesCubit c) {
    return InkWell(
      onTap: () async {
        MemoryUpsertBts.show(context).then(
          (result) {
            if (result == true) {
              c.fetchMemories();
            }
          },
        );
      },
      child: Column(
        children: [
          const Icon(Icons.list, size: 64),
          const SizedBox(height: 16),
          const Text('You don\'t have any memories yet'),
          TextButton(
            onPressed: () async {
              MemoryUpsertBts.show(context).then(
                (result) {
                  if (result == true) {
                    c.fetchMemories();
                  }
                },
              );
            },
            style: TextButton.styleFrom(
              backgroundColor: Colors.blue, // Add background color
            ),
            child: const Text(
              'Add new memory',
              style: TextStyle(color: Colors.white), // Set text color to white
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMemoryList(MemoriesCubit cubit, MemoriesState state) {
    return ListView.builder(
      itemCount: state.memories.length,
      itemBuilder: (context, index) {
        final memory = state.memories[index];
        return MemoryCard(cubit: cubit, state: state, memory: memory);
      },
    );
  }

  //Widget buildBottomView() => const SizedBox();

// //   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: const Text('Memories'),
//         actions: [
//           IconButton(
//             icon: const Icon(Icons.camera_alt),
//             onPressed: () {
//               // Handle camera action
//             },
//           )
//         ],
//       ),
//       body: CustomBlocBuilder<MainState, MainCubit>(
//         cubit: mainCubit,
//         viewBuilder: (context, cubit, state) {
//           if (state.status == MemoriesStatus.loading) {
//             return const Center(child: CircularProgressIndicator());
//           } else if (state.status == MemoriesStatus.success) {
//             return ListView.builder(
//               itemCount: state.memories.length,
//               itemBuilder: (context, index) {
//                 final memory = state.memories[index];
//                 return MemoryCard(memory: memory); // Custom widget for each memory
//               },
//             );
//           } else if (state.status == MemoriesStatus.error) {
//             return Center(child: Text(state.errorMessage!));
//           } else {
//             return Container(); // Or show something else in the initial state
//           }
//         },
//       ),
//     );
//   }
}

class MemoryCard extends StatelessWidget {
  final MemoriesCubit cubit;
  final MemoriesState state;
  final MemoryModel memory;

  const MemoryCard({super.key, required this.cubit, required this.state, required this.memory});

  void handleEdit(context) {
    MemoryUpsertBts.show(context, memory: memory).then(
      (result) {
        if (result == true) {
          cubit.fetchMemories();
        }
      },
    );
  }

  GestureTapCallback? onTapCard(BuildContext context, List<ImagePagerItem> items) {
    if (memory.userId == state.accountUuid) {
      return () => handleEdit(context);
    } else
      return onTapImage(context, items, 0);
  }

  GestureTapCallback? onTapImage(BuildContext context, List<ImagePagerItem> items, int? initialIndex) {
    return () => ImageViewerPager.show(
          context: context,
          items: items,
          initialIndex: initialIndex ?? 0,
          doubleTapZoomable: true,
          swipeDismissible: true,
        );
  }

  @override
  Widget build(BuildContext context) {
    final imageItems = memory.files!
        .map((item) => ImagePagerItem(
              imageUrl: item.fileUrlMd!,
              // title: memory.name ?? '',
              title: '',
              description: memory.caption ?? '',
              dateTime: item.createdAt!.toDateTime(),
              activityId: memory.activityId,
              activityName: cubit.getActivityById(memory.activityId!)?.name,
            ))
        .toList();
    return Card(
      margin: const EdgeInsets.all(8.0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.0),
      ),
      child: InkWell(
        onTap: onTapCard(context, imageItems),
        borderRadius: BorderRadius.circular(16.0),
        child: _buildCardBody(context, cubit, memory, imageItems),
      ),
    );
  }

  Widget _buildCardBody(BuildContext context, MemoriesCubit c, MemoryModel memory, List<ImagePagerItem> imageItems) {
    final activityName = cubit.getActivityById(memory.activityId!)?.name;
    return Stack(
      children: [
        /* if (memory.userId == state.accountUuid)
          Positioned(
            top: 2,
            right: 12,
            child: SizedBox(
              width: 36,
              height: 36,
              child: PopupMenuButton(
                onSelected: (command) {
                  switch (command) {
                    case 'edit':
                      handleEdit(context);
                      break;
                    default:
                      break;
                  }
                },
                padding: const EdgeInsets.all(4.0),
                icon: ImageAssetCustom(imagePath: Assets.icons.threeDot.path, color: Colors.black, size: 32),
                //Menu
                itemBuilder: (BuildContext context) => [
                  PopupMenuItem(value: 'edit', child: Text(LocaleKeys.edit.tr(), style: AppStyle.textSmR)),
                ],
                color: Colors.white,
                menuPadding: const EdgeInsets.symmetric(vertical: 4),
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
              ),
            ),
          ), */
        Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(left: 8.0, right: 8.0, top: 8.0),
                        child: Text(
                          (memory.createdAt?.toDateTime() ?? DateTime.now()).formatDateMemories(),
                          style: const TextStyle(
                            color: Colors.black,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      if (memory.activityId != null && activityName != null)
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: MemoriesActivityInfo(
                            activityId: memory.activityId,
                            activityName: activityName,
                          ),
                        ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: ExpandableCaption(
                          text: memory.caption ?? '',
                          style: const TextStyle(fontSize: 14.0, color: Colors.black),
                        ),
                      )
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8.0),
            StaggeredGridView.countBuilder(
              crossAxisCount: 4,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              padding: const EdgeInsets.all(8.0),
              itemCount: memory.files?.length,
              itemBuilder: (context, index) {
                return ClipRRect(
                  borderRadius: BorderRadius.circular(10.0),
                  child: GestureDetector(
                    onTap: onTapImage(context, imageItems, index),
                    child: _buildImage(context, cubit, memory.files![index].fileUrlMd!),
                  ),
                );
              },
              staggeredTileBuilder: (index) {
                if (index == 0) {
                  return const StaggeredTile.count(4, 2); // Big image spanning 4 columns and 2 rows
                } else {
                  return const StaggeredTile.count(2, 2); // Smaller images spanning 2 columns and 2 rows
                }
              },
              mainAxisSpacing: 8.0,
              crossAxisSpacing: 8.0,
            ),
          ],
        )
      ],
    );
  }

  Widget _buildImage(BuildContext context, MemoriesCubit c, String url, {double height = 100, double width = double.infinity}) {
    return CachedNetworkImage(
      imageUrl: url,
      height: height,
      width: width,
      fit: BoxFit.cover,
      placeholder: (context, url) => Container(
        height: height,
        width: width,
        color: Colors.grey,
        child: const Center(child: CircularProgressIndicator()),
      ),
      errorWidget: (context, url, error) => Container(
        height: height,
        width: width,
        color: Colors.grey,
        child: const Icon(Icons.error, color: Colors.red),
      ),
    );
  }
}
