import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/stream/base_stream_builder.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/event.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/calendar/calendar_cubit.dart';
import 'package:family_app/screen/main/calendar/calendar_state.dart';
import 'package:family_app/screen/main/calendar/overlay/calendar_switch_overlay.dart';
import 'package:family_app/screen/main/calendar/widget/calendar_overlay.dart';
import 'package:family_app/screen/main/event/upsert_event/upsert_event_parameter.dart';
import 'package:family_app/utils/bottom_sheet.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:family_app/widget/appbar_custom.dart';
import 'package:family_app/widget/circle_item.dart';
import 'package:family_app/widget/image_asset_custom.dart';
import 'package:family_app/widget/member_horizontal_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:month_picker_dialog/month_picker_dialog.dart';
import 'package:syncfusion_flutter_calendar/calendar.dart';

import 'setting/calendar_setting_screen.dart';
import 'widget/day_range_event_view.dart';
import 'widget/event_month_calendar.dart';
import 'widget/event_day_week_calendar.dart';

@RoutePage()
class CalendarPage extends BaseBlocProvider<CalendarState, CalendarCubit> {
  const CalendarPage({super.key});

  @override
  Widget buildPage() => const CalendarScreenView();

  @override
  CalendarCubit createCubit() => CalendarCubit(
        eventRepository: locator.get(),
        accountService: locator.get(),
        calendarService: locator.get(),
      );
}

class CalendarScreenView extends StatefulWidget {
  const CalendarScreenView({super.key});

  @override
  State<CalendarScreenView> createState() => _CalendarScreenViewState();
}

enum FLCalendarView {
  agenda,
  day,
  week,
  month,
}

class _CalendarScreenViewState extends BaseBlocPageState<CalendarScreenView, CalendarState, CalendarCubit>
    with SingleTickerProviderStateMixin {
  late final CalendarSwitchOverlay overlay;
  final GlobalKey calendarKey = GlobalKey(debugLabel: 'calendarKey');

  @override
  Color get backgroundColor => appTheme.lightGreyColor;

  @override
  void initState() {
    super.initState();
    overlay = CalendarSwitchOverlay(
      provider: this,
      itemOverlayClick: (calendarView) {
        final cubit = context.read<CalendarCubit>();
        cubit.onChangeCalendarView(calendarView);
      },
    );
  }

  @override
  void dispose() {
    overlay.dispose();
    super.dispose();
  }

  @override
  bool get showBack => false;

  @override
  void onTapScreen(BuildContext context) {
    overlay.hideOVerlay();
    // super.onTapScreen(context);
  }

  @override
  Widget buildAppBar(BuildContext context, CalendarCubit cubit, CalendarState state) {
    return CustomAppBar2(
      title: LocaleKeys.calendar.tr(),
      showBack: true,
      actions: [
        CircleItem(
          onTap: () async {
            print("Settings button pressed");
            await BottomSheetUtils.showHeight(context, child: const CalendarSettingPage());
            cubit.onFetchEvent();
            // CalendarSettingsBottomSheet.show(
            //   context,
            //   title: LocaleKeys.setting.tr(),
            //   initialSettings: {
            //     'default': true,
            //     'dueDate': true,
            //     'holiday': true,
            //   }, // Pass initial settings if any
            //   onSettingsChanged: (newSettings) {
            //     // Handle settings change
            //     print('Settings changed: $newSettings');
            //   },
            // );
          },
          backgroundColor: appTheme.blackColor.withOpacity(.05),
          padding: padding(all: 2),
          child: Assets.icons.icSettingOutline.svg(width: 32, height: 32),
        ),
      ],
    );
  }

  // @override
  // List<Widget>? appBarActions(CalendarCubit cubit, CalendarState state) {
  //   return [
  //     GestureDetector(
  //       onTap: () {
  //         if (isViewer) {
  //           showSimpleToast(LocaleKeys.viewer_not_permission_event.tr());
  //           return;
  //         }
  //         context.pushRoute(UpsertEventRoute(upsertEventParameter: UpsertEventParameter()));
  //       },
  //       behavior: HitTestBehavior.opaque,
  //       child: RoundItemView(child: Assets.images.add.image(width: 16, height: 16)),
  //     )
  //   ];
  // }

  @override
  Widget buildBody(BuildContext context, CalendarCubit cubit, CalendarState state) {
    cubit.calendarController.selectedDate = DateTime.now();

    return Stack(children: [
      Padding(
        padding: padding(horizontal: 6.0, top: 8.0),
        child: _buildMainContent(context, cubit, state),
      ),
      Positioned(
        bottom: 16.0,
        right: 16.0,
        child: FloatingActionButton(
          onPressed: () {
            // // Handle the tap event here
            logd('Floating Action Button Pressed');
            final selectedDate = cubit.calendarController.selectedDate;
            logd("selectedDate: $selectedDate and now is: ${DateTime.now()}");

            if (selectedDate != null) {
              // Handle the tap event here
              if (selectedDate.isBeforeNow) {
                // can't create event in the past
                showSimpleToast(LocaleKeys.unable_to_create_event_in_the_past.tr());
                return;
              }
              print('Selected Date: $selectedDate');
              // Example: Navigate to a new event creation screen with the selected date
              cubit.onCreateNewEvent(context, selectedDate);
            } else {
              print('No date selected');
            }
          },
          backgroundColor: Colors.transparent,
          child: SvgPicture.asset(Assets.icons.iconAdd.path),
        ),
      ),
    ]);
  }

  Widget _buildFilterAndMemberView(CalendarCubit cubit, CalendarState state) {
    return Container(
      padding: padding(bottom: 8),
      child: Row(
        children: [
          //  _buildMemberView(cubit, state),
          _buildMonthYearView(cubit, state),
          _buildFilterView(cubit, state),
        ],
      ),
    );
  }

  Widget _buildMainContent(BuildContext context, CalendarCubit cubit, CalendarState state) {
    if (state.calendarViewFL == FLCalendarView.agenda) {
      return SingleChildScrollView(
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.only(top: 16, bottom: 8, right: 16, left: 16),
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.all(Radius.circular(20)),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildFilterAndMemberView(cubit, state),
                  _buildMonthlyHeader(context, state),
                  SizedBox(
                    height: MediaQuery.of(context).size.height * 0.4,
                    child: EventCalendar(cubit: cubit, state: state, onCalendarTap: onCalendarTap),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 8),
            StreamBuilder<DateTime>(
                stream: cubit.selectedDateStreamController.stream,
                initialData: DateTime.now(),
                builder: (context, snapshot) {
                  if (snapshot.data == null) {
                    return const SizedBox.shrink();
                  }
                  return DayRangeEventView(
                      key: ValueKey(snapshot.data),
                      events: state.models,
                      currentDay: snapshot.data!,
                      onTap: (event) {
                        cubit.onTapEvent(context, event);
                      });
                }),
            const SizedBox(height: 8),
          ],
        ),
      );
    } else if (state.calendarViewFL == FLCalendarView.day || state.calendarViewFL == FLCalendarView.week) {
      return Column(
        children: [
          Container(
            padding: const EdgeInsets.only(top: 8),
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
              ),
              child: _buildFilterAndMemberView(cubit, state)),
          _buildMonthlyHeader(context, state),
          Expanded(
            child: SingleChildScrollView(
              child: Container(
                margin: const EdgeInsets.only(bottom: 8),
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(20),
                    bottomRight: Radius.circular(20),
                  ),
                ),
                child: EventDayCalendar(cubit: cubit, state: state, onCalendarTap: onCalendarTap),
              ),
            ),
          ),
        ],
      );
    }

    return Container(
      padding: const EdgeInsets.only(top: 8),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          _buildFilterAndMemberView(cubit, state),
          _buildMonthlyHeader(context, state),
          Expanded(child: EventCalendar(cubit: cubit, state: state, onCalendarTap: onCalendarTap)),
        ],
      ),
    );
  }

  Widget _buildMemberView(CalendarCubit cubit, CalendarState state) {
    return Expanded(
      child: BaseStreamBuilder(
          controller: cubit.accountService.memberInFamily,
          builder: (members) {
            return MemberHorizontalView(accounts: members);
          }),
    );
  }

  Widget _buildMonthYearView(CalendarCubit cubit, CalendarState state) {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.only(left: 4),
        child: InkWell(
          onTap: () async {
            // final newMonth = DateTime(state.currentMonth.year, state.currentMonth.month + 1);
            // cubit.calendarController.displayDate = newMonth;
            var now = DateTime.now();
            if (state.calendarViewFL == FLCalendarView.day || state.calendarViewFL == FLCalendarView.week) {
              //show popup select day
              final date = state.currentMonth;
              final DateTime? datePicked = await showDatePicker(
                context: context,
                initialDate: date,
                firstDate: DateTime(now.year - 100),
                lastDate: DateTime(now.year + 100),
              );
              if (datePicked != null && datePicked != state.currentMonth) {
                cubit.calendarController.displayDate = datePicked;
                cubit.emit(state.copyWith(currentMonth: datePicked));
              }
            } else {
              showMonthPicker(
                context: context,
                initialDate: state.currentMonth,
              ).then((date) {
                if (date != null) {
                  cubit.calendarController.displayDate = date;
                  cubit.emit(state.copyWith(currentMonth: date));
                }
              });
            }
          },
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              if (state.calendarViewFL == FLCalendarView.day)
                Text(
                    state.calendarViewFL == FLCalendarView.day
                        ? state.currentMonth.timeddMMMyyyy
                        : state.currentMonth.MM_YYYY,
                    style: AppStyle.bold20V2())
              else ...[
                Text(
                  state.currentMonth.MMM,
                  style: AppStyle.bold20V2(),
                ),
                const SizedBox(width: 4),
                Text(
                  state.currentMonth.year.toString(),
                  style: AppStyle.bold20V2(color: appTheme.grayV2),
                ),
              ],
              SvgPicture.asset(
                Assets.icons.icVerticalDoubleChevron.path,
                width: 24.w,
                height: 24.h,
                colorFilter: ColorFilter.mode(appTheme.grayV2, BlendMode.srcATop),
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFilterView(CalendarCubit cubit, CalendarState state) {
    return Row(
      children: [
        GestureDetector(
          onTap: () => overlay.showOverlay(context, calendarKey, state.calendarViewFL),
          child: Container(
              padding: padding(all: 8),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: appTheme.blackColor.withValues(alpha: .05),
              ),
              child: ImageAssetCustom(
                key: calendarKey,
                imagePath: state.calendarViewFL.imagePath,
                size: 20,
              )),
        ),
        const SizedBox(width: 17),
        GestureDetector(
          onTap: () {
            context.pushRoute(const SearchRoute());
            overlay.hideOVerlay();
          },
          child: Container(
              padding: padding(all: 8),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: appTheme.blackColor.withValues(alpha: .05),
              ),
              child: Assets.images.search.image(width: 20, height: 20)),
        ),
      ],
    );
  }

  void onCalendarTap(
      BuildContext context, CalendarTapDetails calendarTapDetails, CalendarCubit cubit, CalendarState state) {
    logd("onCalendarTap: ${calendarTapDetails.targetElement} => calendarTapDetails.date => ${calendarTapDetails.date}");
    overlay.hideOVerlay();
    cubit.selectedDateStreamController.sink.add(calendarTapDetails.date ?? DateTime.now());
    // cubit.emit(state.copyWith(currentDate: calendarTapDetails.date));
    if (calendarTapDetails.targetElement == CalendarElement.calendarCell) {
      return; // do nothing
    }

    // 1. Check if the Agenda view is enabled, if so, and the tap is on the agenda cell, and also on 1 of the event, show the event detail

    // .   else if the tap is on Agenda view but no event, don't do anything.
    // 2. If the tap is on the month view, don't do anything.

    logd("calendarTapDetails: $calendarTapDetails , target: ${calendarTapDetails.targetElement}");
    if (calendarTapDetails.appointments?.isNotEmpty ?? false) {
      logd("number of appointments: ${calendarTapDetails.appointments!.length}");
      //when user tap on the exact event, then lenght is 1.
      if (calendarTapDetails.appointments!.length == 1) {
        cubit.onTapEvent(context, calendarTapDetails.appointments!.first as EventModels);
        // context.pushRoute(DetailEventRoute(
        //     parameter: DetailEventParameter(eventModels: calendarTapDetails.appointments!.first as EventModels)));
      } else {
        // tapping on the Date itself, it should come here and .. do nothing
      }
      return;
    }
  }

  _buildMonthlyHeader(BuildContext context, CalendarState state) {
    if (state.calendarViewFL == FLCalendarView.month || state.calendarViewFL == FLCalendarView.agenda) {
      List<String> daysOfWeek = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];
      if (!state.weekStartForMonday) {
        daysOfWeek = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
      }
      return Container(
        padding: padding(vertical: 8, horizontal: 0),
        margin: const EdgeInsets.only(left: 4, right: 4),
        decoration: BoxDecoration(
          color: appTheme.backgroundV2,
          borderRadius: const BorderRadius.all(Radius.circular(10)),
        ),
        child: Row(
          children: List.generate(
            daysOfWeek.length,
            (index) => Expanded(
              child: Center(
                child: Text(
                  daysOfWeek[index],
                  style: AppStyle.regular14V2(color: index == 5 || index == 6 ? appTheme.grayV2 : appTheme.blackColor),
                ),
              ),
            ),
          ),
        ),
      );
    } else if (state.calendarViewFL == FLCalendarView.week) {
      return Container(
        padding: padding(vertical: 10),
        margin: const EdgeInsets.only(left: 4, right: 4),
        // decoration: BoxDecoration(
        //   color: appTheme.backgroundV2,
        //   borderRadius: const BorderRadius.all(Radius.circular(10)),
        // ),
        child: Row(
          children: [
            SizedBox(width: 50),
            ...List.generate(
              state.currentDates.length,
              (index) {
                var isToday = state.currentDates[index].isToday;
                return Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        state.currentDates[index].EEE,
                        style: AppStyle.regular14V2(
                            color: index == 5 || index == 6 ? appTheme.grayV2 : appTheme.blackColor),
                      ),
                      Container(
                        width: 30,
                        height: 30,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: isToday ? appTheme.calendarDateBackgroundColor : appTheme.transparentColor,
                        ),
                        child: Text(
                          state.currentDates[index].day.toString(),
                          style: AppStyle.bold14V2(
                              color: isToday
                                  ? appTheme.whiteText
                                  : state.isHoliday(state.currentDates[index])
                                      ? appTheme.orangeColorV2
                                      : index == 5 || index == 6
                                          ? appTheme.grayV2
                                          : appTheme.blackColor),
                        ),
                      ),
                    ],
                  ),
                );
              },
            )
          ],
        ),
      );
    } else {
      return const SizedBox.shrink();
    }
  }
}
