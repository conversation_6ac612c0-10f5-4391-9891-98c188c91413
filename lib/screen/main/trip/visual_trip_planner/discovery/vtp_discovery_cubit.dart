import 'dart:async';
import 'dart:convert';

import 'package:auto_route/auto_route.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/service/app_service.dart';

import 'package:family_app/data/model/hotel_booking_model.dart';
import 'package:family_app/data/model/hotel_model.dart';
import 'package:family_app/data/model/trip_model.dart';
import 'package:family_app/data/usecase/hotel_booking_usecase.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/trip/trip_parameter.dart';
import 'package:family_app/screen/main/trip/visual_trip_planner/constants/vtp_constants.dart';
import 'package:family_app/screen/main/trip/visual_trip_planner/discovery/vtp_destination_focus_screen.dart';
import 'package:family_app/screen/main/trip/visual_trip_planner/discovery/vtp_discovery_parameter.dart';
import 'package:family_app/screen/main/trip/visual_trip_planner/discovery/vtp_discovery_state.dart';
import 'package:family_app/screen/main/trip/visual_trip_planner/model/trip_plan_data.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:flutter/material.dart';
import 'package:web_socket_channel/web_socket_channel.dart';

/// Cubit for managing the VTP Discovery feature with WebSocket connection
class VTPDiscoveryCubit extends BaseCubit<VTPDiscoveryState> {
  /// Parameters for the VTP Discovery
  final VTPDiscoveryParameter parameter;

  /// WebSocket channel for communication
  late WebSocketChannel _channel;

  /// Timer for handling connection timeouts
  Timer? _timeoutTimer;

  VTPDiscoveryCubit({
    required this.parameter,
  }) : super(VTPDiscoveryState(
          searchTerm: parameter.initialSearchTerm,
          selectedVibes: parameter.initialSelectedVibes,
        ));

  @override
  Future<void> onInit() async {
    super.onInit();
    await _initializeWebSocketChannel();
    _startTimeout();

    // Handle different flow types
    switch (parameter.flowType) {
      case VTPFlowType.discovery:
        AppLogger.d(
            "VTP Discovery initializing with search term: ${parameter.initialSearchTerm}");

        // If initial search term and vibes are provided, perform search
        if (parameter.initialSearchTerm.isNotEmpty) {
          await searchDestinations(
            parameter.initialSearchTerm,
            parameter.initialSelectedVibes,
          );
        }
        break;

      case VTPFlowType.direct:
        AppLogger.d(
            "VTP Direct initializing with destination: ${parameter.directTripData?.destination}");

        // For direct flow, we don't need to perform an initial search
        // The user will trigger the trip planning when they're ready
        break;
    }
  }

  @override
  Future<void> close() async {
    AppLogger.d("Closing VTP Discovery Cubit");
    _closeWebSocketChannel();
    _timeoutTimer?.cancel();
    super.close();
  }

  /// Initialize the WebSocket channel
  Future<void> _initializeWebSocketChannel() async {
    AppLogger.d("Initializing VTP Discovery WebSocket channel");
    final token = parameter.token;

    try {
      _channel = WebSocketChannel.connect(
        Uri.parse('wss://vrelay-vn1.5gencare.com/ai/ws?authorization=$token'),
      );
      _channel.stream.listen(
        _handleStreamData,
        onError: _handleStreamError,
        onDone: _handleStreamDone,
      );

      // Wait for the connection to be established
      await _channel.ready;

      emit(state.copyWith(connState: VTPConnState.success, loading: false));
    } catch (e) {
      AppLogger.e("WebSocket connection error: $e");
      emit(state.copyWith(connState: VTPConnState.none, loading: false));
      _handleStreamError(e);
    }
  }

  /// Close the WebSocket channel
  void _closeWebSocketChannel() {
    _channel.sink.close();
    AppLogger.d("WebSocket connection closed");
  }

  /// Start a timeout timer
  void _startTimeout() {
    AppLogger.d("Starting timeout timer");
    _timeoutTimer?.cancel();
    _timeoutTimer = Timer(const Duration(seconds: 60), _handleTimeout);
  }

  /// Handle timeout events
  void _handleTimeout() {
    _timeoutTimer?.cancel();
    _handleStreamError('Connection timeout');
    emit(state.copyWith(
      connState: VTPConnState.timeout,
      loading: false,
      isWaitingForResponse: false,
      isTripPlanLoading: false, // Also clear trip plan loading state on timeout
      tripPlanErrorMessage:
          state.isTripPlanLoading ? 'Trip plan request timed out' : null,
    ));
  }

  /// Handle incoming data from the WebSocket
  void _handleStreamData(dynamic data) {
    AppLogger.d("Received data from WebSocket: $data");
    _timeoutTimer?.cancel();

    try {
      final response = jsonDecode(data) as Map<String, dynamic>;

      if (response.isEmpty) {
        _handleStreamError('Empty response received');
        return;
      }

      // Handle AI chat response
      if (response['command'] == WebSocketCommands.aiChatRespond) {
        AppLogger.d("Processing AI chat response: ${response['command']}");
        _handleAIChatResponse(response);
      } else {
        AppLogger.d("Unknown command received: ${response['command']}");
        // Log the full response for debugging
        AppLogger.d("Full response: $response");
      }
    } catch (e, stackTrace) {
      AppLogger.e("Error parsing WebSocket data: $e");
      AppLogger.e("Stack trace: $stackTrace");
      AppLogger.e("Raw data received: $data");
      _handleStreamError('Error parsing response: $e');
    }
  }

  /// Handle AI chat response from the server
  void _handleAIChatResponse(Map<String, dynamic> response) {
    try {
      final String message = response['message'] ?? '';

      // Try to determine which prompt was used
      String prompt = response['prompt'] ?? '';

      // If prompt is not directly in the response, check the original_message
      if (prompt.isEmpty) {
        final originalMessage =
            response['original_message'] as Map<String, dynamic>?;
        if (originalMessage != null && originalMessage.containsKey('prompt')) {
          prompt = originalMessage['prompt'] as String;
          AppLogger.d("Found prompt in original_message: $prompt");
        }
      }

      AppLogger.d("Handling AI chat response with prompt: $prompt");

      if (message.isEmpty) {
        emit(state.copyWith(
          destinationOptions: [],
          connState: VTPConnState.success,
          loading: false,
          isWaitingForResponse: false,
        ));
        return;
      }

      // Try to parse the message as JSON
      try {
        // Clean the message if it's wrapped in markdown code block format
        String cleanedMessage = message;

        logd("message length: ${message.length}");

        // Check if the message starts with markdown code block syntax
        if (message.trim().startsWith('```')) {
          // Extract the JSON content from the markdown code block
          final RegExp codeBlockRegex =
              RegExp(r'```(?:json)?\n([\s\S]*?)\n```');
          final Match? match = codeBlockRegex.firstMatch(message);

          if (match != null && match.groupCount >= 1) {
            cleanedMessage = match.group(1) ?? message;
            AppLogger.d(
                "Extracted JSON from markdown code block: $cleanedMessage");
          } else {
            // If regex doesn't match but still starts with ```, try a simpler approach
            cleanedMessage =
                message.replaceFirst(RegExp(r'```(?:json)?\n'), '');
            final endIndex = cleanedMessage.lastIndexOf('\n```');
            if (endIndex != -1) {
              cleanedMessage = cleanedMessage.substring(0, endIndex);
            }
            AppLogger.d(
                "Extracted JSON using simple approach: $cleanedMessage");
          }
        }

        // Parse the JSON data
        final dynamic jsonData = jsonDecode(cleanedMessage);

        // Handle different types of responses based on the prompt
        if (prompt == AIPrompts.vtpPlan) {
          AppLogger.d("Detected full trip plan response (vtp_plan)");
          // This is a full trip plan response from madlibs screen
          _handleFullTripPlanResponse(jsonData);
        } else {
          AppLogger.d(
              "Detected destination discovery response (default or vtp_discover)");
          // Default case: handle destination discovery response
          _handleDestinationDiscoveryResponse(jsonData);
        }
      } catch (e, stackTrace) {
        AppLogger.e("Error parsing message as JSON: $e");
        AppLogger.e("Stack trace: $stackTrace");
        AppLogger.e("Original message: $message");

        // If the message is not valid JSON, emit an error state
        emit(state.copyWith(
          destinationOptions: [],
          connState: VTPConnState.error,
          loading: false,
          isWaitingForResponse: false,
          errorMessage: 'Invalid response format: $e',
        ));
      }
    } catch (e) {
      _handleStreamError('Error parsing response: $e');
    }
  }

  /// Handle destination discovery response (list of destination options)
  void _handleDestinationDiscoveryResponse(dynamic jsonData) {
    try {
      // The message should be a JSON array of destination objects
      final List<dynamic> destinationsJson = jsonData as List<dynamic>;

      if (destinationsJson.isEmpty) {
        // Empty JSON array is an error case
        emit(state.copyWith(
          destinationOptions: [],
          connState: VTPConnState.error,
          loading: false,
          isWaitingForResponse: false,
          errorMessage:
              'No destinations found. Please refine your search terms.',
        ));
        return;
      }

      // Convert each JSON object to a DestinationOption
      final List<DestinationOption> destinations = destinationsJson.map((json) {
        // Extract the fields from the JSON object
        final String city = json['city'] ?? '';
        final String country = json['country'] ?? '';
        final String subtitle = json['subtitle'] ?? '';
        final List<String> touristActivities =
            (json['tourist_activities'] as List<dynamic>?)?.cast<String>() ??
                [];

        // Create a DestinationOption with the extracted fields
        return DestinationOption(
          name: '$city, $country',
          description: subtitle,
          iconType:
              _getIconTypeForDestination(city, country, touristActivities),
          touristActivities: touristActivities,
        );
      }).toList();

      emit(state.copyWith(
        destinationOptions: destinations,
        connState: VTPConnState.success,
        loading: false,
        isWaitingForResponse: false,
      ));
    } catch (e, stackTrace) {
      AppLogger.e("Error handling destination discovery response: $e");
      AppLogger.e("Stack trace: $stackTrace");
      _handleStreamError('Error parsing destinations: $e');
    }
  }

  /// Handle full trip plan response (Trip model)
  void _handleFullTripPlanResponse(dynamic jsonData) async {
    try {
      // Log the full trip plan data for now
      AppLogger.d("Received full trip plan: $jsonData");

      // Try to parse the data into a Trip object
      if (jsonData is Map<String, dynamic>) {
        try {
          // Check if we need to extract the trip data from a wrapper object
          Map<String, dynamic> tripData = jsonData;

          // Some responses might have the trip data nested inside another object
          if (jsonData.containsKey('ai_trip')) {
            tripData = jsonData['ai_trip'] as Map<String, dynamic>;
            AppLogger.d("Found nested trip data");
          }

          // Log the structure of the trip data for debugging
          AppLogger.d("Trip data keys: ${tripData.keys.toList()}");

          // Try to parse the Trip object
          final Trip trip = Trip.fromJson(tripData);

          // Log successful parsing and trip details
          AppLogger.d("Successfully parsed Trip: ${trip.name}");
          AppLogger.d("Trip details: ${trip.city}, ${trip.country}");
          AppLogger.d("Trip dates: ${trip.fromDate} to ${trip.toDate}");
          AppLogger.d(
              "Trip has ${trip.itinerary.length} days in the itinerary");

          // Log each day of the itinerary
          for (int i = 0; i < trip.itinerary.length; i++) {
            final itinerary = trip.itinerary[i];
            AppLogger.d(
                "Day ${i + 1} - Accommodation: ${itinerary.accommodation}");
            AppLogger.d(
                "Day ${i + 1} - Activities: ${itinerary.activities?.length ?? 0} activities");

            // Log each activity for this day
            if (itinerary.activities != null) {
              for (int j = 0; j < itinerary.activities!.length; j++) {
                final activity = itinerary.activities![j];
                AppLogger.d(
                    "  Activity ${j + 1}: ${activity.time} - ${activity.description}");
              }
            }
          }

          // Enhanced: Search for hotels based on trip preferences
          Trip updatedTrip = trip;
          if (trip.hotelPreferences != null) {
            AppLogger.d("Trip has hotel preferences, searching for hotels...");
            updatedTrip = await _searchAndAddHotelToTrip(trip);
          } else {
            AppLogger.d("No hotel preferences found in trip");
          }

          // Update state to indicate trip plan loading is complete and store the trip
          emit(state.copyWith(
            connState: VTPConnState.success,
            loading: false,
            isWaitingForResponse: false,
            isTripPlanLoading: false, // Trip plan loading is complete
            tripPlanErrorMessage: null, // Clear any previous error
            aiTrip: updatedTrip, // Store the updated trip in the state
          ));
        } catch (e, stackTrace) {
          AppLogger.e("Error parsing Trip object: $e");
          AppLogger.e("Stack trace: $stackTrace");

          // Update state to indicate trip plan loading failed
          emit(state.copyWith(
            connState: VTPConnState.error,
            loading: false,
            isWaitingForResponse: false,
            isTripPlanLoading:
                false, // Trip plan loading is complete (with error)
            tripPlanErrorMessage:
                'Error parsing trip plan: $e', // Set error message
          ));

          _handleStreamError('Error parsing Trip object: $e');
        }
      } else {
        AppLogger.e(
            "Expected a JSON object for Trip, got ${jsonData.runtimeType}");

        // Update state to indicate trip plan loading failed
        emit(state.copyWith(
          connState: VTPConnState.error,
          loading: false,
          isWaitingForResponse: false,
          isTripPlanLoading:
              false, // Trip plan loading is complete (with error)
          tripPlanErrorMessage:
              'Invalid trip plan data format', // Set error message
        ));

        _handleStreamError('Invalid Trip data format');
      }
    } catch (e) {
      // Update state to indicate trip plan loading failed
      emit(state.copyWith(
        connState: VTPConnState.error,
        loading: false,
        isWaitingForResponse: false,
        isTripPlanLoading: false, // Trip plan loading is complete (with error)
        tripPlanErrorMessage:
            'Error processing trip plan: $e', // Set error message
      ));

      _handleStreamError('Error processing trip plan: $e');
    }
  }

  /// Helper method to determine the icon type based on the destination and activities
  String _getIconTypeForDestination(
      String city, String country, List<String> activities) {
    // Check activities for keywords to determine the icon type
    final String activitiesText = activities.join(' ').toLowerCase();

    if (activitiesText.contains('beach') ||
        activitiesText.contains('ocean') ||
        activitiesText.contains('sea') ||
        activitiesText.contains('swim')) {
      return 'beach';
    } else if (activitiesText.contains('mountain') ||
        activitiesText.contains('hiking') ||
        activitiesText.contains('trek')) {
      return 'mountain';
    } else if (activitiesText.contains('temple') ||
        activitiesText.contains('shrine') ||
        activitiesText.contains('worship')) {
      return 'temple';
    } else if (activitiesText.contains('food') ||
        activitiesText.contains('restaurant') ||
        activitiesText.contains('cuisine') ||
        activitiesText.contains('dining')) {
      return 'food';
    } else if (activitiesText.contains('nature') ||
        activitiesText.contains('park') ||
        activitiesText.contains('garden')) {
      return 'nature';
    } else if (activitiesText.contains('museum') ||
        activitiesText.contains('gallery') ||
        activitiesText.contains('exhibition')) {
      return 'museum';
    }

    // Default to city icon if no specific activities match
    return 'city';
  }

  /// Handle WebSocket stream errors
  void _handleStreamError(dynamic error) {
    _timeoutTimer?.cancel();
    AppLogger.e("WebSocket error: $error");

    emit(state.copyWith(
      connState: VTPConnState.error,
      loading: false,
      isWaitingForResponse: false,
      errorMessage: 'Error: $error',
    ));
  }

  /// Handle WebSocket stream completion
  void _handleStreamDone() {
    AppLogger.d("WebSocket connection closed");
    _timeoutTimer?.cancel();

    emit(state.copyWith(
      connState: VTPConnState.done,
      loading: false,
      isWaitingForResponse: false,
    ));
  }

  /// Search for destinations with the given search term and vibes
  Future<void> searchDestinations(
      String searchTerm, List<String> selectedVibes) async {
    // Cancel existing timer if any
    _timeoutTimer?.cancel();

    // Update state with new search parameters
    emit(state.copyWith(
      searchTerm: searchTerm,
      selectedVibes: selectedVibes,
      isWaitingForResponse: true,
      loading: true,
    ));

    // Create the search data as a JSON string
    final searchData =
        jsonEncode({"searchTerm": searchTerm, "selectedVibes": selectedVibes});

    // Create and send the message in the format required by the AI service
    final jsonMessage = jsonEncode({
      "command": WebSocketCommands.aiChat,
      "message": searchData,
      "auto_create": AutoCreateValues.falseValue,
      "prompt": AIPrompts.vtpDiscover
    });

    AppLogger.d("Sending search message: $jsonMessage");
    _channel.sink.add(jsonMessage);

    // Start new timeout timer
    _startTimeout();
  }

  /// Plan a trip with direct information
  Future<void> planDirectTrip(DirectTripData directData) async {
    // Cancel existing timer if any
    _timeoutTimer?.cancel();

    // Update state to indicate waiting for response
    emit(state.copyWith(
      isWaitingForResponse: true,
      isTripPlanLoading: true,
      tripPlanErrorMessage: null,
    ));

    // Create the trip plan data
    final tripPlanData = TripPlanData(
      tripIntent: TripIntentValues.direct,
      data: directData.toJson(),
    );

    // Convert the trip plan data to a JSON string
    final tripPlanJson = jsonEncode(tripPlanData.toJson());

    // Create the WebSocket message in the format required by the AI service
    final jsonMessage = jsonEncode({
      "command": WebSocketCommands.aiChat,
      "message": tripPlanJson,
      "auto_create": AutoCreateValues.falseValue,
      "prompt": AIPrompts.vtpPlan
    });

    AppLogger.d("Sending direct trip plan message: $jsonMessage");

    // Send the message
    _channel.sink.add(jsonMessage);

    // Start timeout timer
    _startTimeout();
  }

  /// Reset the connection state and clear any error messages
  void resetConnectionState() {
    AppLogger.d("Resetting connection state and clearing error messages");
    emit(state.copyWith(
      connState: VTPConnState.none,
      errorMessage: null,
      loading: false,
      isWaitingForResponse: false,
      isTripPlanLoading: false,
      tripPlanErrorMessage: null,
    ));
  }

  /// Reset the trip plan in the state
  void resetTripPlan() {
    AppLogger.d("Resetting trip plan");
    emit(state.copyWith(
      aiTrip: null,
    ));
  }

  /// Send a custom WebSocket message
  /// This can be used by other components to send messages through the same WebSocket connection
  void sendWebSocketMessage(String jsonMessage) {
    AppLogger.d("Sending custom WebSocket message: $jsonMessage");

    // Check if this is a trip plan request
    bool isTripPlanRequest = false;
    try {
      final Map<String, dynamic> message = jsonDecode(jsonMessage);
      if (message.containsKey('prompt') &&
          message['prompt'] == AIPrompts.vtpPlan) {
        isTripPlanRequest = true;
        AppLogger.d("Detected trip plan request");
      }
    } catch (e) {
      // If we can't parse the message, assume it's not a trip plan request
      AppLogger.e("Error parsing WebSocket message: $e");
    }

    // Update state to indicate waiting for response
    emit(state.copyWith(
      isWaitingForResponse: true,
      isTripPlanLoading: isTripPlanRequest ? true : state.isTripPlanLoading,
      tripPlanErrorMessage:
          isTripPlanRequest ? null : state.tripPlanErrorMessage,
    ));

    // Send the message
    _channel.sink.add(jsonMessage);

    // Start timeout timer
    _startTimeout();
  }

  /// Search for hotels based on trip preferences and locations, supporting multiple hotels for multi-city trips
  Future<Trip> _searchAndAddHotelToTrip(Trip trip) async {
    try {
      final hotelPreferences = trip.hotelPreferences!;

      // Extract check-in and check-out dates from preferences or trip dates
      DateTime checkInDate = hotelPreferences.checkInDate ?? trip.fromDate;
      DateTime checkOutDate = hotelPreferences.checkOutDate ?? trip.toDate;

      AppLogger.d("Searching hotels for multi-location trip");
      AppLogger.d("Base check-in: $checkInDate, Base check-out: $checkOutDate");

      // Get the hotel booking usecase
      final HotelBookingUseCase hotelUseCase =
          locator.get<HotelBookingUseCase>();

      // Collect all unique locations with their IATA city codes from the trip
      Map<String, String> locationCodes = _extractLocationCodesFromTrip(trip);
      AppLogger.d(
          "Found ${locationCodes.length} unique locations with codes: $locationCodes");

      if (locationCodes.isEmpty) {
        AppLogger.w("No valid city codes found for hotel search");
        return trip; // Return original trip if no valid city codes
      }

      List<HotelBookingModel> selectedHotels = [];
      List<String> failedLocations = [];
      List<String> noHotelsLocations = [];

      // Search hotels for each location using city codes
      int locationIndex = 0;
      for (MapEntry<String, String> locationEntry in locationCodes.entries) {
        String cityName = locationEntry.key;
        String cityCode = locationEntry.value;
        AppLogger.d(
            "Searching hotels for location: $cityName (code: $cityCode)");

        // Create location-specific preferences using city code for API call
        HotelPreferences locationPreferences = HotelPreferences(
          location: cityCode, // Use IATA city code for API call
          checkInDate: hotelPreferences.checkInDate,
          checkOutDate: hotelPreferences.checkOutDate,
          numberOfGuests: hotelPreferences.numberOfGuests,
          roomType: hotelPreferences.roomType,
          starRating: hotelPreferences.starRating,
          amenities: hotelPreferences.amenities,
          budget: hotelPreferences.budget,
        );

        // Calculate dates for this location (for multi-city trips, we might need different date ranges)
        HotelStayPeriod locationStayPeriod = _calculateLocationDateRange(
            trip, cityName, locationIndex, locationCodes.length);

        try {
          // Search for hotels using the new method with city code
          List<HotelModel> hotels =
              await hotelUseCase.searchHotelsByPreferences(
            locationPreferences,
            locationStayPeriod.checkIn,
            locationStayPeriod.checkOut,
          );

          AppLogger.d(
              "Found ${hotels.length} hotels for $cityName (code: $cityCode)");

          if (hotels.isNotEmpty) {
            // TODO: Add placeholder for filtering function that can be called later
            List<HotelModel> filteredHotels =
                _filterHotelsForTrip(hotels, locationPreferences);

            // Keep the first item in the list
            HotelModel selectedHotel =
                filteredHotels.isNotEmpty ? filteredHotels.first : hotels.first;

            AppLogger.d("Selected hotel for $cityName: ${selectedHotel.name}");

            // Create hotel booking for this location using existing HotelBookingModel
            HotelBookingModel hotelBooking = HotelBookingModel(
              hotelId: selectedHotel.id,
              hotelName: selectedHotel.name,
              imageUrl: selectedHotel.imageURL,
              location: cityName, // Use city name for display
              provider: "Auto-selected for trip planning",
              checkInDate: locationStayPeriod.checkIn
                  .toIso8601String()
                  .split('T')[0], // YYYY-MM-DD format
              checkOutDate: locationStayPeriod.checkOut
                  .toIso8601String()
                  .split('T')[0], // YYYY-MM-DD format
              bookingResults: [
                {
                  "itineraryDayIndexes":
                      _getItineraryDaysForLocation(trip, cityName),
                  "notes": "Auto-selected based on trip preferences",
                  "selectionTimestamp": DateTime.now().toIso8601String(),
                  "cityCode": cityCode, // Store the city code used for search
                }
              ],
            );

            selectedHotels.add(hotelBooking);
          } else {
            // No hotels found for this location
            AppLogger.w(
                "No hotels found for location: $cityName (code: $cityCode)");
            noHotelsLocations.add(cityName);

            // Create a placeholder booking to indicate the issue
            HotelBookingModel placeholderBooking =
                _createPlaceholderHotelBooking(cityName, locationStayPeriod,
                    trip, "No hotels available for this location");
            selectedHotels.add(placeholderBooking);
          }
        } catch (e) {
          // API call failed for this location
          AppLogger.e(
              "Hotel search API failed for location $cityName (code: $cityCode): $e");
          failedLocations.add(cityName);

          // Create a placeholder booking to indicate the failure
          HotelBookingModel placeholderBooking = _createPlaceholderHotelBooking(
              cityName,
              locationStayPeriod,
              trip,
              "Hotel search failed: ${e.toString()}");
          selectedHotels.add(placeholderBooking);
        }

        locationIndex++; // Increment for next location
      }

      // Log summary of hotel search results
      AppLogger.d("Hotel search completed:");
      AppLogger.d("- Total locations: ${locationCodes.length}");
      AppLogger.d(
          "- Successful hotels: ${selectedHotels.length - failedLocations.length - noHotelsLocations.length}");
      AppLogger.d(
          "- Failed API calls: ${failedLocations.length} (${failedLocations.join(', ')})");
      AppLogger.d(
          "- No hotels available: ${noHotelsLocations.length} (${noHotelsLocations.join(', ')})");

      // Create a new Trip object with the selected hotels
      return Trip(
        name: trip.name,
        isDateConfirmed: trip.isDateConfirmed,
        fromDate: trip.fromDate,
        toDate: trip.toDate,
        country: trip.country,
        city: trip.city,
        color: trip.color,
        description: trip.description,
        itinerary: trip.itinerary,
        familyId: trip.familyId,
        uuid: trip.uuid,
        hotelPreferences: trip.hotelPreferences,
        flightPreferences: trip.flightPreferences,
        selectedHotels: selectedHotels.isNotEmpty ? selectedHotels : null,
        cityCode: trip.cityCode,
        additionalCities: trip.additionalCities,
        additionalCityCode: trip.additionalCityCode,
      );
    } catch (e) {
      AppLogger.e("Error searching for hotels: $e");
      // Return the original trip if hotel search fails
      return trip;
    }
  }

  /// Create a placeholder hotel booking when hotel search fails or returns no results
  HotelBookingModel _createPlaceholderHotelBooking(
    String location,
    HotelStayPeriod stayPeriod,
    Trip trip,
    String errorMessage,
  ) {
    return HotelBookingModel(
      hotelId: "placeholder_${location.toLowerCase().replaceAll(' ', '_')}",
      hotelName: "Hotel search unavailable",
      imageUrl: null,
      location: location,
      provider: "System placeholder",
      checkInDate: stayPeriod.checkIn.toIso8601String().split('T')[0],
      checkOutDate: stayPeriod.checkOut.toIso8601String().split('T')[0],
      bookingResults: [
        {
          "itineraryDayIndexes": _getItineraryDaysForLocation(trip, location),
          "notes": errorMessage,
          "status": "failed",
          "isPlaceholder": true,
          "failureTimestamp": DateTime.now().toIso8601String(),
        }
      ],
    );
  }

  /// Placeholder method for filtering hotels for the trip
  /// This can be enhanced later to support more sophisticated filtering
  List<HotelModel> _filterHotelsForTrip(
    List<HotelModel> hotels,
    HotelPreferences preferences,
  ) {
    // For now, just return the original list
    // Future enhancements can include filtering by:
    // - Star rating
    // - Amenities
    // - Budget range
    // - Room type
    // - Distance from city center
    // - User reviews/ratings
    AppLogger.d("Applying trip-specific hotel filters (placeholder)");
    return hotels;
  }

  /// Extract all unique locations from the trip and map them to their IATA city codes
  /// Returns a map of city name to city code for hotel search API
  Map<String, String> _extractLocationCodesFromTrip(Trip trip) {
    Map<String, String> locationCodes = {};

    // Add main city with its code
    if (trip.city != null &&
        trip.city!.isNotEmpty &&
        trip.cityCode != null &&
        trip.cityCode!.isNotEmpty) {
      locationCodes[trip.city!] = trip.cityCode!;
    }

    // Add additional cities with their codes
    if (trip.additionalCities != null && trip.additionalCityCode != null) {
      for (int i = 0;
          i < trip.additionalCities!.length &&
              i < trip.additionalCityCode!.length;
          i++) {
        String cityName = trip.additionalCities![i];
        String cityCode = trip.additionalCityCode![i];
        if (cityName.isNotEmpty && cityCode.isNotEmpty) {
          locationCodes[cityName] = cityCode;
        }
      }
    }

    AppLogger.d("Extracted location codes: $locationCodes");
    return locationCodes;
  }

  /// Extract all unique locations from the trip (main city, additional cities, and activity cities)
  /// This method is kept for backward compatibility but should use _extractLocationCodesFromTrip for hotel search
  List<String> _extractLocationsFromTrip(Trip trip) {
    Set<String> locations = {};

    // Add main city
    if (trip.city != null && trip.city!.isNotEmpty) {
      locations.add(trip.city!);
    }

    // Add additional cities
    if (trip.additionalCities != null) {
      locations.addAll(trip.additionalCities!.where((city) => city.isNotEmpty));
    }

    // If no locations found, use hotel preferences location as fallback
    if (locations.isEmpty && trip.hotelPreferences?.location != null) {
      locations.add(trip.hotelPreferences!.location!);
    }

    return locations.toList();
  }

  /// Get a map of locations to their corresponding itinerary day indexes
  Map<String, List<int>> _getLocationDaysFromItinerary(Trip trip) {
    Map<String, List<int>> locationDays = {};

    for (int dayIndex = 0; dayIndex < trip.itinerary.length; dayIndex++) {
      var itinerary = trip.itinerary[dayIndex];

      // Check if any activity in this day has a city specified
      if (itinerary.activities != null) {
        Set<String> citiesInThisDay = {};

        for (var activity in itinerary.activities!) {
          if (activity.city != null && activity.city!.isNotEmpty) {
            citiesInThisDay.add(activity.city!);
          }
        }

        // Add this day index to all cities found in this day
        for (String city in citiesInThisDay) {
          if (!locationDays.containsKey(city)) {
            locationDays[city] = [];
          }
          locationDays[city]!.add(dayIndex);
        }
      }
    }

    // If no cities found in activities, assign all days to main city and additional cities
    if (locationDays.isEmpty) {
      AppLogger.d(
          "No cities found in activities, using main city and additional cities");

      List<String> allLocations = [];
      if (trip.city != null && trip.city!.isNotEmpty) {
        allLocations.add(trip.city!);
      }
      if (trip.additionalCities != null) {
        allLocations
            .addAll(trip.additionalCities!.where((city) => city.isNotEmpty));
      }

      if (allLocations.isNotEmpty) {
        // Distribute days equally among locations
        int totalDays = trip.itinerary.length;
        int daysPerLocation = (totalDays / allLocations.length).ceil();

        for (int i = 0; i < allLocations.length; i++) {
          String location = allLocations[i];
          List<int> daysForLocation = [];

          int startDay = i * daysPerLocation;
          int endDay = (i == allLocations.length - 1)
              ? totalDays - 1 // Last location gets remaining days
              : (startDay + daysPerLocation - 1);

          for (int day = startDay; day <= endDay && day < totalDays; day++) {
            daysForLocation.add(day);
          }

          locationDays[location] = daysForLocation;
        }
      }
    }

    AppLogger.d("Location days mapping: $locationDays");
    return locationDays;
  }

  /// Calculate hotel stay period for a specific location based on itinerary days
  HotelStayPeriod _calculateLocationDateRange(
      Trip trip, String location, int locationIndex, int totalLocations) {
    // Count actual days for each location from the itinerary
    Map<String, List<int>> locationDays = _getLocationDaysFromItinerary(trip);

    List<int> daysForLocation = locationDays[location] ?? [];

    if (daysForLocation.isEmpty) {
      AppLogger.w(
          "No itinerary days found for location: $location, using fallback logic");
      // Fallback to equal division if no specific days found
      Duration tripDuration = trip.toDate.difference(trip.fromDate);
      int daysPerLocation = (tripDuration.inDays / totalLocations).ceil();

      DateTime checkIn =
          trip.fromDate.add(Duration(days: locationIndex * daysPerLocation));
      DateTime checkOut = locationIndex == totalLocations - 1
          ? trip.toDate
          : checkIn.add(Duration(days: daysPerLocation));

      if (checkOut.isAfter(trip.toDate)) {
        checkOut = trip.toDate;
      }

      return HotelStayPeriod(checkIn: checkIn, checkOut: checkOut);
    }

    // Calculate check-in and check-out dates based on actual itinerary days
    int firstDay =
        daysForLocation.reduce((a, b) => a < b ? a : b); // Min day index
    int lastDay =
        daysForLocation.reduce((a, b) => a > b ? a : b); // Max day index

    DateTime checkIn = trip.fromDate.add(Duration(days: firstDay));
    DateTime checkOut = trip.fromDate
        .add(Duration(days: lastDay + 1)); // +1 because check-out is next day

    // Ensure check-out doesn't exceed trip end date
    if (checkOut.isAfter(trip.toDate)) {
      checkOut = trip.toDate;
    }

    AppLogger.d(
        "Location $location: Days $daysForLocation, Check-in: $checkIn, Check-out: $checkOut");

    return HotelStayPeriod(checkIn: checkIn, checkOut: checkOut);
  }

  /// Get itinerary day indexes that correspond to a specific location
  List<int> _getItineraryDaysForLocation(Trip trip, String location) {
    List<int> dayIndexes = [];

    for (int i = 0; i < trip.itinerary.length; i++) {
      var itinerary = trip.itinerary[i];

      // Check if any activity in this day is in the specified location
      if (itinerary.activities != null) {
        bool hasLocationActivity = itinerary.activities!.any((activity) =>
            activity.city != null &&
            activity.city!.toLowerCase() == location.toLowerCase());

        if (hasLocationActivity) {
          dayIndexes.add(i);
        }
      }
    }

    // If no specific days found, return empty list (hotel applies to whole trip)
    return dayIndexes;
  }
}

/// Helper class to represent a hotel stay period with check-in and check-out dates
class HotelStayPeriod {
  final DateTime checkIn;
  final DateTime checkOut;

  HotelStayPeriod({required this.checkIn, required this.checkOut});

  /// Get the duration of the hotel stay in days
  int get durationInDays => checkOut.difference(checkIn).inDays;

  /// Check if the stay period is valid (check-out after check-in)
  bool get isValid => checkOut.isAfter(checkIn);

  @override
  String toString() {
    return 'HotelStayPeriod{checkIn: $checkIn, checkOut: $checkOut, duration: ${durationInDays} days}';
  }
}
