import 'dart:math';

import 'package:auto_route/auto_route.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:dotted_line/dotted_line.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/local/local_storage.dart';
import 'package:family_app/data/model/hotel_booking_model.dart';
import 'package:family_app/data/model/transfer_model.dart';
import 'package:family_app/data/model/trip_model.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/chat/chat_context.dart';
import 'package:family_app/screen/main/chat/chat_parameter.dart';
import 'package:family_app/screen/main/trip/trip_detail_parameter.dart';
import 'package:family_app/screen/main/trip/trip_select_activity_type_parameter.dart';
import 'package:family_app/screen/main/trip/trip_select_activity_type_screen.dart';
import 'package:family_app/utils/bottom_sheet.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/loading.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:family_app/widget/circle_item.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:timeline_tile/timeline_tile.dart';

import 'trip_detail_cubit.dart';
import 'trip_detail_state.dart';

@RoutePage()
class TripDetailPage extends BaseBlocProvider<TripDetailState, TripDetailCubit> {
  const TripDetailPage({required this.parameter, super.key});

  final TripDetailParameter parameter;

  @override
  Widget buildPage() => const TripDetailView();

  @override
  TripDetailCubit createCubit() => TripDetailCubit(activityRepository: locator.get(), parameter: parameter);
}

class TripDetailView extends StatefulWidget {
  const TripDetailView({super.key});

  @override
  State<TripDetailView> createState() => _TripDetailViewState();
}

class _TripDetailViewState extends BaseBlocPageState<TripDetailView, TripDetailState, TripDetailCubit> {
  @override
  void initState() {
    super.initState();
    isTopSafeArea = false;
  }

  @override
  bool listenWhen(TripDetailState previous, TripDetailState current) {
    if (current.isSaving) {
      showLoading();
    } else if (previous.isSaving && !current.isSaving) {
      if (current.saveTripSuccess) {
        AppLogger.d('Save trip success 11');
        context.pushRoute(const ActivityHomeRoute());
      } else {
        AppLogger.d('Save trip failed');
      }
    }
    return super.listenWhen(previous, current);
  }

  @override
  Widget buildAppBar(BuildContext context, TripDetailCubit cubit, TripDetailState state) {
    return Stack(
      children: [
        Column(children: [
          AspectRatio(
            aspectRatio: 4 / 3, // Keep the width-height ratio
            child: Container(
              width: double.infinity, // Match the width of the screen
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: CachedNetworkImageProvider(state.activity?.imagePath ?? ''),
                  fit: BoxFit.cover,
                ),
              ),
            ),
          ),
        ]),
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  state.activity?.name ?? '',
                  style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 20.0),
                ),
                Text(
                  "${state.activity?.fromDate?.toDateTime().MMM_d_yyyy ?? ''} - ${state.activity?.toDate?.toDateTime().MMM_d_yyyy ?? ''}",
                  style: TextStyle(color: Colors.white, fontWeight: FontWeight.normal, fontSize: 16.0),
                ),
              ],
            ),
          ),
        ),
        Positioned(
          top: 44,
          left: 4,
          right: 4,
          child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
            CircleItem(
                onTap: () => context.popRoute(),
                padding: padding(all: 2),
                backgroundColor: appTheme.blackColor.withOpacity(.2),
                child: Transform.rotate(
                  angle: pi,
                  child: Assets.icons.arrowRight.svg(
                      width: 32.w, height: 32.w, colorFilter: const ColorFilter.mode(Colors.white, BlendMode.srcIn)),
                )),
            /* Nguyen disable sharing for now
            const Expanded(child: SizedBox()),
            CircleItem(
              onTap: () {
                // Handle map icon press
              },
              padding: padding(all: 8),
              backgroundColor: appTheme.blackColor.withOpacity(.2),
              child: const Icon(Icons.map, color: Colors.white, size: 22),
            ),
            const SizedBox(width: 4),
            CircleItem(
              onTap: () {
                // Handle share icon press
              },
              padding: padding(all: 8),
              backgroundColor: appTheme.blackColor.withOpacity(.2),
              child: const Icon(Icons.share, color: Colors.white, size: 22),
            ),
            const SizedBox(width: 4),
            CircleItem(
              onTap: () {
                // Handle settings icon press
                // _goToEditTrip(state);
              },
              padding: padding(all: 8),
              backgroundColor: appTheme.blackColor.withOpacity(.2),
              child: const Icon(Icons.settings, color: Colors.white, size: 22),
            ),
           */
          ]),
        ),
      ],
    );
  }

  @override
  Widget buildBody(BuildContext context, TripDetailCubit cubit, TripDetailState state) {
    if (state.loading) {
      return const Center(child: CircularProgressIndicator());
    }

    return DefaultTabController(
      length: state.days.length,
      initialIndex: state.selectedIndex,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TabBar(
            onTap: (value) => cubit.setSelectedIndex(value),
            isScrollable: true,
            tabs: state.days.map((day) => Tab(text: day)).toList(),
            tabAlignment: TabAlignment.start,
            indicatorSize: TabBarIndicatorSize.tab,
          ),
          Expanded(
            child: TabBarView(
              children: state.activity?.itinerary?.asMap().entries.map((entry) {
                    int index = entry.key;
                    Itinerary itinerary = entry.value;
                    return _buildActivities(context, cubit, state, itinerary, index);
                  }).toList() ??
                  [],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActivities(
      BuildContext context, TripDetailCubit cubit, TripDetailState state, Itinerary itinerary, int dayIndex) {
    final timelineList = cubit.getTimelineList(state.activity!, dayIndex);
    final count = timelineList.length;
    return ListView.builder(
      itemCount: count + 1, // Add 1 for the "Add new item"
      itemBuilder: (context, index) {
        if (index == count) {
          return _buildAddNewItemTile(context, cubit, state, itinerary, dayIndex);
        }

        final timelineItem = timelineList[index];
        if (timelineItem.data is TransferModel) {
          return _buildTransferTimelineTile(context, cubit, state, timelineItem.data, index, count);
        } else if (timelineItem.data is HotelBookingModel) {
          return _buildHotelTile(context, cubit, state, timelineItem.data as HotelBookingModel, index, count);
        } else {
          return _buildActivityTimelineTile(context, cubit, state, timelineItem.data as Activity, index, count);
        }
      },
    );
  }

  Widget _buildHotelTile(BuildContext context, TripDetailCubit cubit, TripDetailState state, HotelBookingModel hotel,
      int index, int length) {
    return TimelineTile(
      alignment: TimelineAlign.manual,
      indicatorStyle: IndicatorStyle(
        width: 32, // Increased indicator size
        height: 32,
        // padding: const EdgeInsets.all(4),
        color: Colors.white,
        indicator: Container(
          // Custom indicator with icon
          decoration: const BoxDecoration(
            shape: BoxShape.circle,
            // color: Colors.white, // Or your icon background color
          ),
          child: Assets.icons.icHotel.svg(width: 32, height: 32),
        ),
      ),
      beforeLineStyle: const LineStyle(
        color: Colors.grey,
        thickness: 2, // Adjust line thickness
      ),
      // isFirst: index == 0, // Mark the first item
      // isLast: index == length - 1, // Mark the last item
      lineXY: 0.09,
      // Adjust this value (0.0 to 1.0) to position the line
      endChild: Padding(
        padding: const EdgeInsets.only(left: 16, right: 16, top: 4, bottom: 4),
        child: _buildHotelItemView(context, cubit, state, hotel),
      ),
    );
  }

  Widget _buildTransferTimelineTile(BuildContext context, TripDetailCubit cubit, TripDetailState state,
      TransferModel transfer, int index, int length) {
    return TimelineTile(
      alignment: TimelineAlign.manual,
      indicatorStyle: IndicatorStyle(
        width: 32, // Increased indicator size
        height: 32,
        // padding: const EdgeInsets.all(4),
        color: Colors.white,
        indicator: Container(
          // Custom indicator with icon
          decoration: const BoxDecoration(
            shape: BoxShape.circle,
            // color: Colors.white, // Or your icon background color
          ),
          child: Assets.icons.icTransfer.svg(width: 32, height: 32),
        ),
      ),
      beforeLineStyle: const LineStyle(
        color: Colors.grey,
        thickness: 2, // Adjust line thickness
      ),
      // isFirst: index == 0, // Mark the first item
      // isLast: index == length - 1, // Mark the last item
      lineXY: 0.09,
      // Adjust this value (0.0 to 1.0) to position the line
      endChild: Padding(
        padding: const EdgeInsets.only(left: 16, right: 16, top: 4, bottom: 4),
        child: _buildTransferItemView(context, cubit, state, transfer),
      ),
    );
  }

  Widget _buildTransferItemView(
      BuildContext context, TripDetailCubit cubit, TripDetailState state, TransferModel transfer) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(color: appTheme.borderColor),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              Text(
                transfer.getFromDate(),
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
              Text(
                transfer.ticketNo ?? '',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF4E46B4),
                ),
              ),
              Text(
                transfer.getToDate(),
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                transfer.getFromTime(),
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                // Use Expanded to center the line and icon
                child: Center(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.circle,
                        size: 12,
                        color: Color(0xFF595D62),
                      ),
                      const Expanded(
                        // Use Expanded to make the dotted line fill the space
                        child: DottedLine(
                          direction: Axis.horizontal,
                          lineThickness: 2.0,
                          // Thickness of the line
                          dashLength: 4.0,
                          // Length of each dash
                          dashColor: Color(0xFF595D62),
                          dashGapLength: 4.0,
                          // Gap between dashes
                          dashRadius: 0.0, // Make it round if needed
                        ),
                      ),
                      const SizedBox(width: 4),
                      SvgPicture.asset(
                        Assets.icons.icFlight.path,
                        width: 20,
                        height: 20,
                        colorFilter: const ColorFilter.mode(Color(0xFF595D62), BlendMode.srcIn),
                      ),
                      const SizedBox(width: 4),
                      const Expanded(
                        // Use Expanded to make the dotted line fill the space
                        child: DottedLine(
                          direction: Axis.horizontal,
                          lineThickness: 2.0,
                          // Thickness of the line
                          dashLength: 4.0,
                          // Length of each dash
                          dashColor: Color(0xFF595D62),
                          dashGapLength: 4.0,
                          // Gap between dashes
                          dashRadius: 0.0, // Make it round if needed
                        ),
                      ),
                      const Icon(
                        Icons.circle,
                        size: 12,
                        color: Color(0xFF595D62),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                transfer.getToTime(),
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                flex: 40,
                child: Text(
                  transfer.fromLocation ?? '',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                  maxLines: 3,
                  overflow: TextOverflow.visible,
                  textAlign: TextAlign.start,
                ),
              ),
              Flexible(
                flex: 20,
                child: Text(
                  transfer.getDuration(),
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
              ),
              Flexible(
                flex: 40,
                child: Text(
                  transfer.toLocation ?? '',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                  maxLines: 3,
                  overflow: TextOverflow.visible,
                  textAlign: TextAlign.end,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAddNewItemTile(
      BuildContext context, TripDetailCubit cubit, TripDetailState state, Itinerary itinerary, int dayIndex) {
    return TimelineTile(
      alignment: TimelineAlign.manual,
      indicatorStyle: IndicatorStyle(
        width: 32,
        height: 32,
        color: Colors.white,
        indicator: Container(
          decoration: const BoxDecoration(
            shape: BoxShape.circle,
            // color: Colors.white, // Background color for the add icon
          ),
          child: Assets.icons.icTimelineAdd.svg(width: 32, height: 32), // Add icon
        ),
      ),
      beforeLineStyle: const LineStyle(
        color: Colors.grey,
        thickness: 2,
      ),
      lineXY: 0.09,
      endChild: Padding(
        padding: const EdgeInsets.only(left: 16, right: 16, top: 4, bottom: 4),
        child: SizedBox(
          height: 40,
          child: Align(
            alignment: Alignment.centerLeft,
            child: TextButton(
              onPressed: () {
                cubit.onAddNewItemPressed(context, dayIndex);
              },
              child: const Text(
                'Add new item',
                style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold, color: Color(0xFF4E46B4)),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildActivityTimelineTile(
      BuildContext context, TripDetailCubit cubit, TripDetailState state, Activity activity, int index, int length) {
    final isHotel =
        activity.description.toLowerCase().contains('hotel') || activity.description.toLowerCase().contains('hostel');
    return TimelineTile(
      alignment: TimelineAlign.manual,
      indicatorStyle: IndicatorStyle(
        width: 32, // Increased indicator size
        height: 32,
        // padding: const EdgeInsets.all(4),
        color: Colors.white,
        indicator: Container(
          // Custom indicator with icon
          decoration: const BoxDecoration(
            shape: BoxShape.circle,
            // color: Colors.white, // Or your icon background color
          ),
          child: isHotel
              ? Assets.icons.icHotel.svg(width: 32, height: 32)
              : Assets.icons.icPosition.svg(width: 32, height: 32),
        ),
      ),
      beforeLineStyle: const LineStyle(
        color: Colors.grey,
        thickness: 2, // Adjust line thickness
      ),
      // isFirst: index == 0, // Mark the first item
      // isLast: index == length - 1, // Mark the last item
      lineXY: 0.09,
      // Adjust this value (0.0 to 1.0) to position the line
      endChild: Padding(
        padding: const EdgeInsets.only(left: 16, right: 16, top: 4, bottom: 4),
        child: SizedBox(
          height: 170,
          child: _buildActivityItemView2(context, cubit, state, activity),
        ),
      ),
    );
  }

  Widget _buildHotelItemView(
      BuildContext context, TripDetailCubit cubit, TripDetailState state, HotelBookingModel hotel) {
    return InkWell(
      onTap: () {
        if(hotel.bookingResults == null || hotel.bookingResults!.isEmpty) {
          cubit.onBookHotel(context, hotel);
        }
      },
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.0),
          border: Border.all(color: appTheme.borderColor),
        ),
        child: Align(
          alignment: Alignment.centerLeft,
          child: Row(
            children: [
              Container(
                width: 100,
                height: 90,
                decoration: BoxDecoration(
                  image: DecorationImage(
                    image: CachedNetworkImageProvider(hotel.imageUrl ?? ''),
                    fit: BoxFit.fitWidth,
                  ),
                  borderRadius: BorderRadius.circular(8.0),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      hotel.hotelName ?? '',
                      style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
                      maxLines: 2,
                      overflow: TextOverflow.visible,
                    ),
                    _buildLocationView(context, hotel.location ?? ''),
                    // _buildTimeView(context, cubit, state, activity),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActivityItemView2(
      BuildContext context, TripDetailCubit cubit, TripDetailState state, Activity activity) {
    final imageUrl = activity.activityImage;
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(color: appTheme.borderColor),
      ),
      child: Align(
        alignment: Alignment.centerLeft,
        child: Row(
          children: [
            Container(
              width: 100,
              height: 90,
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: imageUrl?.isEmpty == false
                      ? CachedNetworkImageProvider(imageUrl!)
                      : Image.asset(Assets.images.activityType.path).image,
                  fit: BoxFit.fitWidth,
                ),
                borderRadius: BorderRadius.circular(8.0),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              // Ensure the text description is inside the parent view
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center, // Center the text vertically
                children: [
                  Text(
                    activity.description,
                    style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
                    maxLines: 3, // Allow text to break into multiple lines
                    overflow: TextOverflow.visible, // Ensure text is visible
                  ),
                  // const SizedBox(height: 4),
                  _buildLocationView(context, activity.venue ?? ''),
                  // const SizedBox(height: 4),
                  _buildTimeView(context, cubit, state, activity),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLocationView(BuildContext context, String location) {
    return ListTile(
      onTap: () {
        // Open map view
        AppLogger.d('On location pressed');
      },
      contentPadding: EdgeInsets.zero,
      minTileHeight: 0,
      minVerticalPadding: 4,
      minLeadingWidth: 0,
      horizontalTitleGap: 8,
      leading: Assets.icons.icLocation.svg(width: 16, height: 16),
      title: Text(
        location,
        style: const TextStyle(fontSize: 12),
      ),
    );
  }

  Widget _buildTimeView(BuildContext context, TripDetailCubit cubit, TripDetailState state, Activity activity) {
    final isHotel =
        activity.description.toLowerCase().contains('hotel') || activity.description.toLowerCase().contains('hostel');
    return Container(
      child: Row(crossAxisAlignment: CrossAxisAlignment.center, children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              isHotel ? 'Check in' : 'Start time',
              style: const TextStyle(fontSize: 12),
            ),
            // const SizedBox(height: 4),
            Text(
              activity.time == null ? '' : (activity.time == 'AM' ? '10:00' : '16:00'),
              style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
            ),
          ],
        ),
        const SizedBox(width: 16),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              isHotel ? 'Check out' : 'End time',
              style: const TextStyle(fontSize: 12),
            ),
            // const SizedBox(height: 4),
            Text(
              activity.time == null ? '' : (activity.time == 'AM' ? '12:00' : '18:00'),
              style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
            ),
          ],
        ),
      ]),
    );
  }

  @override
  Widget? buildFloatingActionButton(BuildContext context, TripDetailCubit cubit, TripDetailState state) {
    return ElevatedButton(
      onPressed: () {
        AppLogger.d("Ask AI button pressed!");
        _goToEditTrip(state);
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: const Color(0xFF4E46B4),
        // Background color (purple)
        foregroundColor: Colors.white,
        // Text and icon color
        textStyle: const TextStyle(
          fontSize: 16, // Adjust the font size as needed
          fontWeight: FontWeight.w500, // Medium fontWeight
        ),
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
        // Adjust padding as needed
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20), // Rounded corners
        ),
        elevation: 3, // Add a subtle elevation (shadow)
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min, // Ensure the Row takes only the space it needs
        children: [
          Assets.icons.icLightningBolt.svg(
            height: 20, // Adjust icon size
            width: 20, // Ensure the icon is white
          ),
          const SizedBox(width: 8), // Spacing between icon and text
          const Text('Ask AI'),
        ],
      ),
    );
  }

  Future<void> _goToEditTrip(TripDetailState state) async {
    LocalStorage localStorage = locator.get();
    final token = await localStorage.accessToken();
    if (token!.isEmpty) {
      await localStorage.clear();
      context.replaceRoute(const AuthRoute());
    } else {
      context.pushRoute(
          ChatRoute(parameter: ChatParameter(chatContext: ChatContext.getEditTripContext(token, state.activityId))));
    }
  }
}
