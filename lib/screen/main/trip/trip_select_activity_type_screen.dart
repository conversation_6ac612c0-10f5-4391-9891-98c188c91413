import 'package:auto_route/auto_route.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/trip/hotel/hotel_list_parameter.dart';
import 'package:family_app/screen/main/trip/trip_select_activity_type_cubit.dart';
import 'package:family_app/screen/main/trip/trip_select_activity_type_parameter.dart';
import 'package:family_app/screen/main/trip/trip_select_activity_type_state.dart';
import 'package:family_app/screen/main/trip/trip_transfer_upsert_parameter.dart';
import 'package:family_app/utils/loading.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class TripSelectActivityTypeBts extends BaseBlocProvider<TripSelectActivityTypeState, TripSelectActivityTypeCubit> {

  const TripSelectActivityTypeBts({required this.parameter, super.key});
  final TripSelectActivityTypeParameter parameter;

  @override
  Widget buildPage() => const TripSelectActivityTypeScreen();

  @override
  TripSelectActivityTypeCubit createCubit() => TripSelectActivityTypeCubit(
        parameter: parameter,
        familyRepository: locator.get(),
        uploadRepository: locator.get(),
        activityRepository: locator.get(),
      );
}

class TripSelectActivityTypeScreen extends StatefulWidget {
  const TripSelectActivityTypeScreen({super.key});

  @override
  State<TripSelectActivityTypeScreen> createState() => _TripSelectActivityTypeScreenState();
}

class _TripSelectActivityTypeScreenState
    extends BaseBlocPageState<TripSelectActivityTypeScreen, TripSelectActivityTypeState, TripSelectActivityTypeCubit> {
  @override
  bool listenWhen(TripSelectActivityTypeState previous, TripSelectActivityTypeState current) {
    if (current.status == TripSelectActivityTypeStatus.loading) {
      showLoading();
    } else {
      dismissLoading();
    }
    return super.listenWhen(previous, current);
  }

  @override
  Widget buildView(BuildContext context, TripSelectActivityTypeCubit cubit, TripSelectActivityTypeState state) {
    return _buildDialog(context, cubit, state);
  }

  Widget _buildDialog(BuildContext context, TripSelectActivityTypeCubit cubit, TripSelectActivityTypeState state) {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          _buildHeader(context, cubit, state),
          _buildDivider(context),
          const SizedBox(height: 8),
          _buildActivityTypes(context, cubit, state),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context, TripSelectActivityTypeCubit cubit, TripSelectActivityTypeState state) {
    return Container(
      padding: EdgeInsets.all(16),
      child: const Row(
        children: [
          Expanded(
            child: Center(
              child: Text('Select item type', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDivider(BuildContext context) {
    return Container(
      height: 1,
      color: Theme.of(context).dividerColor,
    );
  }

  Widget _buildActivityTypes(
      BuildContext context, TripSelectActivityTypeCubit cubit, TripSelectActivityTypeState state) {
    // return GridView.builder(
    //   shrinkWrap: true,
    //   physics: const NeverScrollableScrollPhysics(),
    //   gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
    //     crossAxisCount: 3,
    //     childAspectRatio: 1,
    //   ),
    //   itemCount: state.activityTypes.length,
    //   itemBuilder: (context, index) {
    //     final activityType = state.activityTypes[index];
    //     return ActivityTypeCard(activityType: activityType);
    //   },
    // );
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 4, // Number of columns
          crossAxisSpacing: 8.0, // Spacing between columns
          mainAxisSpacing: 16.0, // Spacing between rows
          childAspectRatio: 1, // Aspect ratio of each grid item (adjust as needed)
        ),
        itemCount: state.activityTypes.length,
        itemBuilder: (context, index) {
          return ActivityTypeCard(cubit: cubit, state: state, activityType: state.activityTypes[index]);
        },
      ),
    );
  }
}

class ActivityTypeCard extends StatelessWidget {
  final TripSelectActivityTypeCubit cubit;
  final TripSelectActivityTypeState state;
  final ActivityType activityType;

  const ActivityTypeCard({super.key, required this.cubit, required this.state, required this.activityType});

  @override
  Widget build(BuildContext context) {
    final bool isDisabled = activityType.disabled;

    return Opacity(
      opacity: isDisabled ? 0.5 : 1.0, // Apply blur effect if disabled
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10.0),
        ),
        child: InkWell(
          borderRadius: BorderRadius.circular(10.0), // Apply rounded corners to pressed effect
          onTap: isDisabled
              ? null
              : () {
                  // Handle activity type tap
                  AppLogger.d('Tapped on ${activityType.label}');
                  if(activityType.key == 'transfer') {
                    context.pushRoute(TripTransferUpsertRoute(
                      parameter: TripTransferUpsertParameter(activity: state.activity, dayIndex: state.dayIndex),)).then(
                            (value) => {
                          AppLogger.d('Trip transfer upsert returned value: $value'),
                          Navigator.of(context).pop(value),
                        }
                    );
                  }else if(activityType.key == 'hotel') {
                    context.pushRoute(HotelListRoute(parameter: HotelListParameter(state.activity, state.dayIndex)));
                  }

                },
          child: Padding(
            padding: const EdgeInsets.all(4),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SvgPicture.asset(
                  activityType.iconPath,
                  height: 24, // Adjust icon size as needed
                  width: 24,
                ),
                const SizedBox(height: 4),
                Text(
                  activityType.label,
                  style: const TextStyle(fontSize: 12),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
