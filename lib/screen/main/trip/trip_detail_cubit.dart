import 'dart:convert';

import 'package:auto_route/auto_route.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/model/hotel_booking_model.dart';
import 'package:family_app/data/model/trip_model.dart';
import 'package:family_app/data/repository/activity/iactivity_repository.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/trip/hotel/details/hotel_detail_parameter.dart';
import 'package:family_app/screen/main/trip/trip_detail_parameter.dart';
import 'package:family_app/utils/bottom_sheet.dart';
import 'package:family_app/utils/content_provider.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:flutter/src/widgets/framework.dart';

import 'trip_detail_state.dart';
import 'trip_select_activity_type_parameter.dart';
import 'trip_select_activity_type_screen.dart';

class TripDetailCubit extends BaseCubit<TripDetailState> {
  final IActivityRepository activityRepository;
  final TripDetailParameter parameter;

  TripDetailCubit({required this.activityRepository, required this.parameter})
      : super(TripDetailState(
            activityId: parameter.activityId, activity: parameter.activity));

  @override
  void onInit() {
    super.onInit();
    locator.registerSingleton(this);
    fetchImagesAndLocations();
    AppLogger.d("\nTrip Detail Cubit activityId= ${state.activityId}");
  }

  @override
  Future<void> close() {
    locator.unregister<TripDetailCubit>();
    return super.close();
  }

  void setSelectedIndex(int index) {
    emit(state.copyWith(selectedIndex: index));
  }

  Future<void> fetchImagesAndLocations() async {
    emit(state.copyWith(loading: true));

    String? heroImageUrl = '';

    final activity = await activityRepository
        .getActivityById(parameter.activity?.uuid ?? parameter.activityId);

    // //log trip hotel preferences, and flight preferences
    logd("Trip hotel preferences: ${activity?.hotelPreferences}");
    logd("Trip flight preferences: ${activity?.flightPreferences}");

    logd("trip Hotel info: ${activity?.hotelBookings}");

    final days = activity.itinerary?.asMap().entries.map((entry) {
      int index = entry.key; // The index in the list
      return 'Day ${index + 1}';
    }).toList();

    //then for each day activity, search for an image with activity name
    // for (var itinerary in activity.itinerary ?? []) {
    //   if (itinerary.activities != null) {
    //     for (var act1 in itinerary.activities!) {
    //       //search for an image with activity name
    //       Activity act = act1 as Activity;
    //       act.activityImage = await provider.fetchImageUrl(act.venue ?? act.description!);
    //     }
    //   }
    // }

    //SKIP searching google for acitivity image
    if (activity.imagePath == null) {
      await _fetchTripImages(activity);
    }
    heroImageUrl = activity.imagePath;
    emit(state.copyWith(
        loading: false,
        activity: activity,
        heroImageUrl: heroImageUrl,
        days: days));
  }

  Future<void> _fetchTripImages(ActivityModel activity) async {
    try {
      if (activity.city != null) {
        activity.imagePath = await provider.fetchImageUrl(activity.city!);
      } else if (activity.country != null) {
        activity.imagePath = await provider.fetchImageUrl(activity.country!);
      }
      if (activity.fromDate != null && activity.toDate != null) {
        activity.trip_duration = activity.toDate!.toLocalDT
                .difference(activity.fromDate!.toLocalDT)
                .inDays +
            1;
      }
    } catch (e) {
      AppLogger.e('fetchTripImages error: $e');
    }
  }

  Future<void> editTrip() async {
    emit(state.copyWith(isSaving: true));

    try {
      // final result = await activityRepository.createActivity(parameter.activity.trip.toCreateActivityParameter());
      // AppLogger.d('Create trip result: ${jsonEncode(result)}');
      // await parameter.activity.trip.saveTrip();
      AppLogger.d('Save trip success');
    } catch (e) {
      print("Error saving trip: $e");
      // Handle save error
    } finally {
      emit(state.copyWith(isSaving: false, saveTripSuccess: true));
    }
  }

  Future<void> fetchTripDetail() async {
    emit(state.copyWith(loading: true));
    try {
      final result = await activityRepository.getActivityById(state.activityId);
      AppLogger.d('Get trip result: ${jsonEncode(result)}');
      emit(state.copyWith(loading: false, activity: result));
      return;
    } catch (e) {
      AppLogger.e("Error deleting trip: $e");
    }
    emit(state.copyWith(loading: false));
  }

  List<ActivityTimelineItem> getTimelineList(
      ActivityModel activityModel, int dayIndex) {
    List<ActivityTimelineItem> timelineItems = [];
    final itinerary = activityModel.itinerary![dayIndex];
    final dateTime =
        activityModel.fromDate?.toLocalDT.add(Duration(days: dayIndex));
    itinerary.activities?.forEach((activity) {
      // If activity time = 'AM' or 'PM', set the time to 8:00 AM or 8:00 PM to dateTime object
      DateTime timelineDateTime;
      if (activity.time == 'AM') {
        timelineDateTime =
            DateTime(dateTime!.year, dateTime.month, dateTime.day, 12, 0);
      } else {
        timelineDateTime =
            DateTime(dateTime!.year, dateTime.month, dateTime.day, 18, 0);
      }
      timelineItems.add(
          ActivityTimelineItem(dateTime: timelineDateTime, data: activity));
    });
    itinerary.transfers?.forEach((transfer) {
      if (transfer.fromTime == null) {
        return;
      }
      timelineItems.add(ActivityTimelineItem(
          dateTime: transfer.fromTime!.toLocalDT, data: transfer));
    });
    // Sort timeline items by dateTime
    timelineItems.sort((a, b) => a.dateTime.compareTo(b.dateTime));

    activityModel.hotelBookings?.forEach((booking) {
      if (booking.checkInDate != null && booking.checkOutDate != null) {
        var checkInMilis =
            booking.checkInDate!.toLocalDT.millisecondsSinceEpoch;
        var checkOutMilis =
            booking.checkOutDate!.toLocalDT.millisecondsSinceEpoch;
        var dayTripMilis = dateTime!.millisecondsSinceEpoch;

        if (dayTripMilis >= checkInMilis && dayTripMilis < checkOutMilis) {
          // If the trip day is between check-in and check-out dates, add the booking to the timeline
          timelineItems.add(ActivityTimelineItem(
              dateTime: booking.checkInDate!.toLocalDT, data: booking));
        }

        // if(booking.checkInDate!.toLocalDT.isAfter(dateTime!) ||
        //     booking.checkOutDate!.toLocalDT.isBefore(dateTime)) {
        //   timelineItems.add(ActivityTimelineItem(dateTime: booking.checkInDate!.toLocalDT, data: booking));
        // }
      }
    });

    return timelineItems;
  }

  void onAddNewItemPressed(BuildContext context, int dayIndex) {
    BottomSheetUtils.showHeightReturnBool(context,
            height: 0.3,
            child: TripSelectActivityTypeBts(
                parameter:
                    TripSelectActivityTypeParameter(state.activity!, dayIndex)))
        .then((value) {
      AppLogger.d('Add new item pressed return value: $value');
      if (value == true) {
        // Reload the page
        fetchTripDetail();
      }
    });
  }

  Future<void> onBookHotel(
      BuildContext context, HotelBookingModel hotel) async {
    if (parameter.activity == null) return;
    await context.pushRoute(HotelDetailRoute(
        parameter: HotelDetailParameter(
      hotelId: hotel.hotelId ?? '',
      activity: parameter.activity!,
      location: hotel.location,
      dayIndex: state.selectedIndex,
      isSavedHotel: true,
    )));
  }
}
