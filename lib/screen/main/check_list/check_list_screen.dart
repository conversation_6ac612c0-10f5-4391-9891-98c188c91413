import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/check_list/check_list_cubit.dart';
import 'package:family_app/screen/main/check_list/check_list_state.dart';
import 'package:family_app/screen/main/check_list/upsert_list_item/upsert_list_item_parameter.dart';
import 'package:family_app/screen/main/home/<USER>';
import 'package:family_app/screen/main/home/<USER>';
import 'package:family_app/screen/main/widget/list_shopping_todo_view.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/widget/appbar_custom.dart';
import 'package:family_app/widget/bloc/custom_bloc_builder.dart';
import 'package:family_app/widget/circle_item.dart';
import 'package:flutter/material.dart';

@RoutePage()
class CheckListPage extends BaseBlocProvider<CheckListState, CheckListCubit> {
  const CheckListPage({super.key});

  @override
  Widget buildPage() => const CheckListView();

  @override
  CheckListCubit createCubit() => CheckListCubit();
}

class CheckListView extends StatefulWidget {
  const CheckListView({super.key});

  @override
  State<CheckListView> createState() => _CheckListViewState();
}

class _CheckListViewState extends BaseBlocPageState<CheckListView, CheckListState, CheckListCubit> {
  final HomeCubit mainCubit = locator.get();

  @override
  Color get backgroundColor => appTheme.lightGreyColor;

  @override
  Widget buildAppBar(BuildContext context, CheckListCubit cubit, CheckListState state) {
    return CustomAppBar2(
      title: LocaleKeys.lists.tr(),
      showBack: true,
      actions: [
        CustomBlocBuilder<HomeState, HomeCubit>(
            cubit: mainCubit,
            viewBuilder: (context, cubit, state) {
              return GestureDetector(
                onTap: () => cubit.onCreateListItem(context, isViewer, null),
                behavior: HitTestBehavior.opaque,
                child: CircleItem(
                  backgroundColor: appTheme.blackColor.withOpacity(.05),
                  padding: padding(all: 7),
                  child: Assets.images.add.image(width: 22, height: 22),
                ),
              );
            })
      ],
    );
  }

  @override
  Widget buildBody(BuildContext context, CheckListCubit _, CheckListState __) {
    return CustomBlocBuilder<HomeState, HomeCubit>(
      cubit: mainCubit,
      viewBuilder: (context, cubit, state) {
        if (state.listItems.isEmpty) {
          return _buildEmptyView(cubit);
        }
        return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 6.0, vertical: 2),
            child: Container(
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
              ),
              child: ChecklistView(
                cubit: cubit,
                state: state,
                hasNavigateToUpsertList: true,
              ),
            ));
      },
    );
  }

  Widget _buildEmptyView(HomeCubit cubit) {
    return Padding(
      padding: padding(horizontal: 16),
      child: Column(children: [
        SizedBox(
          width: double.infinity,
          height: 150,
          child: Stack(
            children: [
              Align(
                alignment: Alignment.bottomCenter,
                child: Assets.images.emptyList.image(width: 104, height: 116),
              ),
              Positioned(
                top: 0,
                right: 15,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Assets.images.tapHere.image(width: 72, height: 61),
                    const SizedBox(height: 7),
                    Text(LocaleKeys.tap_here_to_create.tr(), style: AppStyle.regular14())
                  ],
                ),
              )
            ],
          ),
        ),
        const SizedBox(height: 18),
        Text(LocaleKeys.choose_to_create.tr(), style: AppStyle.regular16()),
        const SizedBox(height: 19),
        Row(
          children: [
            Expanded(
                child: _buildEmptyTab(cubit,
                    imagePath: Assets.images.shoppingBackground.path,
                    title: LocaleKeys.shopping_list.tr(),
                    type: ListType.Shopping)),
            const SizedBox(width: 16),
            Expanded(
                child: _buildEmptyTab(cubit,
                    imagePath: Assets.images.todoBackground.path,
                    title: LocaleKeys.todo_list.tr(),
                    type: ListType.Todo)),
          ],
        )
      ]),
    );
  }

  Widget _buildEmptyTab(HomeCubit cubit, {String imagePath = '', String title = '', ListType? type}) {
    return GestureDetector(
      onTap: () {
        cubit.onCreateListItem(context, isViewer, type);
      },
      child: Container(
          height: 116,
          width: double.infinity,
          padding: padding(top: 30, horizontal: 16),
          decoration: BoxDecoration(
              image: DecorationImage(image: AssetImage(imagePath), fit: BoxFit.cover),
              borderRadius: BorderRadius.circular(8)),
          child: Text(title, style: AppStyle.medium20(color: appTheme.whiteText))),
    );
  }
}
