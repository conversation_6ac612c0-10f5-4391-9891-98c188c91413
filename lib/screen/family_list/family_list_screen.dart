import 'package:auto_route/auto_route.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/stream/base_stream_builder.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/family_list/family_list_cubit.dart';
import 'package:family_app/screen/family_list/family_list_state.dart';
import 'package:family_app/screen/family_list/member_list/member_list_parameter.dart';
import 'package:family_app/utils/extension/context_ext.dart';
import 'package:family_app/widget/circle_item.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../../data/model/family.dart';
import '../../gen/assets.gen.dart';
import '../../utils/assets/shadow_util.dart';
import '../../widget/appbar_custom.dart';

@RoutePage()
class FamilyListPage
    extends BaseBlocProvider<FamilyListState, FamilyListCubit> {
  const FamilyListPage({super.key});

  @override
  Widget buildPage() => const FamilyListView();

  @override
  FamilyListCubit createCubit() => FamilyListCubit(
        accountService: locator.get(),
        familyRepository: locator.get(),
        mainCubit: locator.get(),
      );
}

class FamilyListView extends StatefulWidget {
  const FamilyListView({super.key});

  @override
  State<FamilyListView> createState() => _FamilyListViewState();
}

class _FamilyListViewState extends BaseBlocPageState<FamilyListView,
    FamilyListState, FamilyListCubit> {
  @override
  Color get backgroundColor => appTheme.backgroundV2;

  @override
  Color? get backgroundAppBarColor => appTheme.background;

  @override
  bool listenWhen(FamilyListState previous, FamilyListState current) {
    if (current.shouldForceCreateFamily) {
      _navigateToCreateFamily();
    }
    return super.listenWhen(previous, current);
  }

  void _navigateToCreateFamily() {
    context.pushRoute(FamilyCreateRoute());
  }

  @override
  void onTapScreen(BuildContext context) {
    super.onTapScreen(context);
  }

  Widget buildAppBar(
      BuildContext context, FamilyListCubit cubit, FamilyListState state) {
    return CustomAppBar2(
      title: LocaleKeys.my_families_text.tr(),
      showBack: true,
      actions: [
        GestureDetector(
          onTap: _navigateToCreateFamily,
          behavior: HitTestBehavior.opaque,
          child: CircleItem(
            backgroundColor: appTheme.blackColor.withOpacity(.05),
            padding: padding(all: 7),
            child: SvgPicture.asset(Assets.icons.icPlus.path),
          ),
        )
      ],
    );
  }

  @override
  Widget buildBody(
      BuildContext context, FamilyListCubit cubit, FamilyListState state) {
    final account = cubit.accountService.account;
    return TwoBaseNormalStreamBuilder(
      firstController: cubit.accountService.myActiveFamily,
      secondController: cubit.accountService.myFamilyBelong,
      builder: (activeFamily, familys) {
        if (familys.isEmpty) {
          // If no families, navigate to create family screen
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _navigateToCreateFamily();
          });
          return const SizedBox.shrink(); // Return empty widget while navigating
        }

        final sortedFamily = familys.toList()
          ..sort((a, b) => b.isActive!.compareTo(a.isActive ?? 0));

        // Separate active and inactive families
        final activeFamilies =
            sortedFamily.where((f) => f.isActive == 1).toList();
        final inactiveFamilies =
            sortedFamily.where((f) => f.isActive != 1).toList();

        // Create a combined list with divider
        final List<Widget> combinedFamilies = [];

        // Add active families
        if (activeFamilies.isNotEmpty) {
          combinedFamilies.addAll(
              activeFamilies.map((family) => _buildFamilyCard(cubit, family)));
        }

        // Add divider if both types exist
        if (activeFamilies.isNotEmpty && inactiveFamilies.isNotEmpty) {
          combinedFamilies.add(
            Padding(
              padding: const EdgeInsets.only(
                  left: 16, right: 16, bottom: 12, top: 0),
              child: Divider(
                  height: 1, thickness: 1, color: appTheme.borderColorV2),
            ),
          );
        }

        // Add inactive families
        if (inactiveFamilies.isNotEmpty) {
          combinedFamilies.addAll(inactiveFamilies
              .map((family) => _buildFamilyCard(cubit, family)));
        }

        return Padding(
          padding: padding(top: 0),
          child: Column(children: [
            SizedBox(height: 8.h2),
            Expanded(
              child: SingleChildScrollView(
                padding: EdgeInsets.only(bottom: context.bottom),
                child: Column(
                  children: [
                    if (combinedFamilies.isNotEmpty)
                      _boxBuilder(combinedFamilies),
                  ],
                ),
              ),
            ),
            // Column(
            //   children: [
            //     // BaseStreamBuilder(
            //     //   controller: cubit.accountService.memberInFamily,
            //     //   builder: (members) => GestureDetector(
            //     //     onTap: () => context.pushRoute(MemberListRoute(family: activeFamily)),
            //     //     child: _boxBuilder(
            //     //       children: [
            //     //         Text(LocaleKeys.active_family_text.tr(), style: AppStyle.regular12(color: appTheme.labelColor)),
            //     //         SizedBox(height: 20.h),
            //     //         UserProfileRow(
            //     //           name: activeFamily?.familyName ?? activeFamily?.fullName ?? '',
            //     //           text: LocaleKeys.members_text
            //     //               .tr(namedArgs: {'field': '${activeFamily?.members ?? members.length}'}),
            //     //           imageUrl: '',
            //     //           widget: otherFamily.isNotEmpty && activeFamily?.familyUuid != account?.uuid
            //     //               ? ItemButtonTapWidget(
            //     //             title: LocaleKeys.set_de_active.tr(),
            //     //             onTap: () => cubit
            //     //                 .deActiveCurrentFamily(activeFamily?.familyUuid ?? account?.familyUuid ?? ''),
            //     //           )
            //     //               : const SizedBox(),
            //     //         ),
            //     //       ],
            //     //     ),
            //     //   ),
            //     // ),
            //     SizedBox(height: 12.h),
            //     if (otherFamily.isNotEmpty) ...[
            //
            //       // _boxBuilder(
            //       //   children: [
            //       //     ListView.separated(
            //       //       shrinkWrap: true,
            //       //       physics: const BouncingScrollPhysics(),
            //       //       padding: padding(),
            //       //       itemBuilder: (context, index) {
            //       //         final family = otherFamily[index];
            //       //         return UserProfileRow(
            //       //           name: family.familyName ?? family.fullName ?? '',
            //       //           text: LocaleKeys.members_text.tr(namedArgs: {'field': '${family.members ?? 0}'}),
            //       //           imageUrl: '',
            //       //           widget: ItemButtonTapWidget(
            //       //               title: LocaleKeys.set_active_text.tr(),
            //       //               onTap: () => cubit.activeFamily(family.familyUuid ?? '')),
            //       //         );
            //       //       },
            //       //       separatorBuilder: (context, index) => Padding(
            //       //         padding: padding(vertical: 15),
            //       //         child: const LineWidget(),
            //       //       ),
            //       //       itemCount: otherFamily.length,
            //       //     )
            //       //   ],
            //       // )
            //     ],
            //   ],
            // ),
          ]),
        );
      },
    );
  }

  Widget _boxBuilder(List<Widget> children) {
    return Container(
      padding: padding(top: 16, bottom: 4),
      margin: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(24.w2),
          boxShadow: ShadowUtil.backgroundShadow),
      child: Column(children: children),
    );
  }

  Widget _buildFamilyCard(FamilyListCubit cubit, Family family) {
    return InkWell(
      onTap: () => context.pushRoute(MemberListRoute(
        parameter: MemberListParameter(
          familyId: family.familyUuid ?? '',
        ),
      )),
      child: Padding(
        padding: const EdgeInsets.only(left: 16, right: 16, bottom: 12),
        child: Container(
          decoration: BoxDecoration(
            color: family.isActive == 1
                ? Colors.green[50]
                : appTheme.transparentWhiteColor,
            borderRadius: BorderRadius.circular(20.w),
            border: family.isActive == 1
                ? Border.all(color: appTheme.greenV2, width: 1)
                : null,
          ),
          child: ListTile(
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
            minTileHeight: 80,
            leading: Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                image: family.photoUrl != null && family.photoUrl!.isNotEmpty
                    ? DecorationImage(
                        image: CachedNetworkImageProvider(family.photoUrl!),
                        fit: BoxFit.cover,
                      )
                    : DecorationImage(
                        image: AssetImage(Assets.images.avatarFamily.path),
                        fit: BoxFit.cover,
                      ),
              ),
            ),
            title: Text(
              family.familyName ?? '',
              style: AppStyle.medium16(color: appTheme.blackText),
            ),
            subtitle: Text(
                LocaleKeys.members_text
                    .tr(namedArgs: {'field': '${family.members ?? 0}'}),
                style: AppStyle.regular12(color: appTheme.grayV2)),
            trailing: Container(
              padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
              decoration: BoxDecoration(
                color: family.isActive == 1
                    ? appTheme.greenV2
                    : appTheme.lightBotColor,
                borderRadius: BorderRadius.circular(20),
              ),
              child: InkWell(
                onTap: () {
                  cubit.activeFamily(family.familyUuid ?? '');
                },
                child: Text(
                  family.isActive == 1 ? "Active" : "Activate",
                  style: AppStyle.bold12(color: appTheme.whiteText),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
