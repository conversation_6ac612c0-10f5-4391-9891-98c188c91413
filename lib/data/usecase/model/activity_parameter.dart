import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/data/model/trip_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'activity_parameter.g.dart';

@JsonSerializable(explicitToJson: true)
class CreateActivityParameter {
  @Json<PERSON>ey(name: 'uuid')
  final String? uuid;
  @Json<PERSON>ey(name: 'name')
  final String name;

  @Json<PERSON>ey(name: 'from_date')
  final String fromDate;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'to_date')
  final String toDate;

  @Json<PERSON><PERSON>(name: 'color')
  final String? color;
  @Json<PERSON>ey(name: 'caption')
  final String? caption;
  @J<PERSON><PERSON><PERSON>(name: 'description')
  final String? description;

  @Json<PERSON><PERSON>(name: 'is_date_confirmed')
  bool? isDateConfirmed;
  @JsonKey(name: 'country')
  String? country;
  @Json<PERSON>ey(name: 'city')
  String? city;

  @JsonKey(name: 'tripImageUrl')
  String? tripImageUrl;

  @J<PERSON><PERSON>ey(name: 'hotel_preferences')
  HotelPreferences? hotelPreferences;

  @Json<PERSON>ey(name: 'flight_preferences')
  FlightPreferences? flightPreferences;

  @Json<PERSON>ey(name: 'city_code')
  String? cityCode;

  @JsonKey(name: 'additional_cities')
  List<String>? additionalCities;

  @JsonKey(name: 'additional_city_code')
  List<String>? additionalCityCode;

  @JsonKey(name: 'included_members')
  final List<String>? includedMembers;

  @JsonKey(name: 'included_events')
  final List<dynamic>? includedEvents;

  @JsonKey(name: 'included_lists')
  final List<String>? includedLists;

  @JsonKey(name: 'family_id')
  final String familyId;

  @JsonKey(name: 'activity_type')
  final String activityType;

  @JsonKey(name: 'itinerary')
  final List<Itinerary> itinerary;

  @JsonKey(name: 'attachments')
  final List<dynamic>? attachments;

  @JsonKey(includeFromJson: false, includeToJson: false)
  final ListType type;

  @JsonKey(includeFromJson: false, includeToJson: false)
  final List<String> oldEventIds;

  @JsonKey(includeFromJson: false, includeToJson: false)
  final List<String> oldListIds;

  @JsonKey(name: 'hotel_bookings')
  final List<String>? hotelBookings;

  // @JsonKey(includeFromJson: false, includeToJson: false)
  // final List<UpsertItemParam> items;

  // @JsonKey(includeFromJson: false, includeToJson: false)
  // final List<UpsertItemParam>? oldItems;

  CreateActivityParameter({
    this.uuid,
    required this.name,
    required this.fromDate,
    required this.toDate,
    required this.color,
    required this.caption,
    required this.description,
    this.isDateConfirmed,
    this.country,
    this.city,
    this.includedMembers = const [],
    this.includedEvents = const [],
    this.includedLists = const [],
    required this.familyId,
    required this.activityType,
    this.itinerary = const [],
    this.type = ListType.Trip,
    this.oldEventIds = const [],
    this.oldListIds = const [],
    this.tripImageUrl = '',
    this.attachments,
    this.hotelPreferences,
    this.flightPreferences,
    this.cityCode,
    this.additionalCities,
    this.additionalCityCode,
    this.hotelBookings,
  });

  factory CreateActivityParameter.fromJson(Map<String, dynamic> json) => _$CreateActivityParameterFromJson(json);

  Map<String, dynamic> toJson() => _$CreateActivityParameterToJson(this);

  CreateActivityParameter copyWith({
    String? uuid,
    String? name,
    String? fromDate,
    String? toDate,
    String? color,
    String? caption,
    String? description,
    bool? isDateConfirmed,
    String? country,
    String? city,
    List<String>? includedMembers,
    List<dynamic>? includedEvents,
    List<String>? includedLists,
    String? familyId,
    String? activityType,
    List<Itinerary>? itinerary,
    ListType? type,
    List<String>? oldEventIds,
    List<String>? oldListIds,
    String? tripImageUrl,
    List<dynamic>? attachments,
    List<String>? hotelBookings,
    HotelPreferences? hotelPreferences,
    FlightPreferences? flightPreferences,
    String? cityCode,
    List<String>? additionalCities,
    List<String>? additionalCityCode,
  }) {
    return CreateActivityParameter(
      uuid: uuid ?? this.uuid,
      name: name ?? this.name,
      fromDate: fromDate ?? this.fromDate,
      toDate: toDate ?? this.toDate,
      color: color ?? this.color,
      caption: caption ?? this.caption,
      description: description ?? this.description,
      isDateConfirmed: isDateConfirmed ?? this.isDateConfirmed,
      country: country ?? this.country,
      city: city ?? this.city,
      includedMembers: includedMembers ?? this.includedMembers,
      includedEvents: includedEvents ?? this.includedEvents,
      includedLists: includedLists ?? this.includedLists,
      familyId: familyId ?? this.familyId,
      activityType: activityType ?? this.activityType,
      itinerary: itinerary ?? this.itinerary,
      type: type ?? this.type,
      oldEventIds: oldEventIds ?? this.oldEventIds,
      oldListIds: oldListIds ?? this.oldListIds,
      tripImageUrl: tripImageUrl ?? this.tripImageUrl,
      attachments: attachments ?? this.attachments,
      hotelPreferences: hotelPreferences ?? this.hotelPreferences,
      flightPreferences: flightPreferences ?? this.flightPreferences,
      cityCode: cityCode ?? this.cityCode,
      additionalCities: additionalCities ?? this.additionalCities,
      additionalCityCode: additionalCityCode ?? this.additionalCityCode,
    );
  }
}
