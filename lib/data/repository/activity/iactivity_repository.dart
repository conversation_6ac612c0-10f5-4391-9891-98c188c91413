import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/model/item.dart';
import 'package:family_app/data/model/list_log_item.dart';
import 'package:family_app/data/repository/ibase_repository.dart';
import 'package:family_app/data/usecase/model/activity_item_parameter.dart';
import 'package:family_app/data/usecase/model/activity_parameter.dart';
import 'package:family_app/data/usecase/model/upsert_item_param.dart';

abstract class IActivityRepository extends IBaseRepository {
  Future<List<ActivityModel>> getUserActivities();
  Future<ActivityModel> createActivity(CreateActivityParameter parameter);
  Future<ActivityModel> updateActivity(String id, CreateActivityParameter parameter);
  Future<List<ActivityModel>> getAllActivities(String familyId, {String? name});
  Future<ActivityModel> getActivityById(String id);
  Future<bool> deleteActivity(String id);

  Future<Item?> createItemInActivity(UpsertItemParam param);
  Future<bool> deleteItemInActivity(String id);
  Future<Item?> updateItemInActivity(String id, UpsertItemParam param);
  Future<List<Item>?> getAllItemInActivity(ActivityItemParameter param);
  // Future<List<ListItem>> getActivityByFamilyId(String familyId);
  Future<List<ListLog>> getAllItemLogInActivity(String activityId);

  Future<List<ActivityModel>> getFamilyActivities(String id, {int limit = 10, int offset = 0});
}
