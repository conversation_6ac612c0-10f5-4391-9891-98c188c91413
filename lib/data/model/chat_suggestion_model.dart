import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:family_app/screen/main/chat/chat_context.dart';
import 'package:rfc_6902/rfc_6902.dart' as rfc;

/// Result class for JSON extraction operations
class _JsonExtractionResult {
  final Map<String, dynamic>? jsonData;
  final String cleanedMessage;
  final bool isPureJson;

  _JsonExtractionResult({
    this.jsonData,
    required this.cleanedMessage,
    this.isPureJson = false,
  });
}

class ChatSuggestionModel {
  final String message;
  final List<String> suggestions;
  final Map<String, dynamic>? aiTrip;
  final Map<String, dynamic>? aiEvent;
  final Map<String, dynamic>? aiList;
  final Map<String, dynamic>? aiTripIntent;
  final bool? editingComplete;

  ChatSuggestionModel({
    required this.message,
    required this.suggestions,
    this.aiTrip,
    this.aiEvent,
    this.aiList,
    this.aiTripIntent,
    this.editingComplete,
  });

  factory ChatSuggestionModel.fromJson(Map<String, dynamic> json,
      {AIPurposeKey? purposeKey, dynamic currentTrip}) {
    // Route to purpose-specific parsing if needed
    if (purposeKey != null) {
      return _parseWithPurpose(json, purposeKey, currentTrip: currentTrip);
    }

    // Default parsing logic (existing behavior)
    return _parseDefault(json);
  }

  /// Default parsing logic for backward compatibility
  static ChatSuggestionModel _parseDefault(Map<String, dynamic> json) {
    final String rawMessage = json['message'] as String;
    List<String> allSuggestions = [];
    Map<String, dynamic>? aiTripData;
    Map<String, dynamic>? aiEventData;
    Map<String, dynamic>? aiListData;
    Map<String, dynamic>? aiTripIntentData;
    String processedMessage = '';

    // Try to extract JSON using the unified method
    final extractionResult = _extractJsonFromMessage(rawMessage);

    if (extractionResult.jsonData != null) {
      final jsonMap = extractionResult.jsonData!;
      _extractSuggestions(jsonMap, allSuggestions);

      if (jsonMap.containsKey('ai_trip')) {
        aiTripData = _normalizeDataTypesForTrip(jsonMap['ai_trip']);
      }
      if (jsonMap.containsKey('ai_event')) {
        aiEventData = jsonMap['ai_event'];
      }
      if (jsonMap.containsKey('ai_list')) {
        aiListData = jsonMap['ai_list'];
      }
      // Check for intent structure
      if (jsonMap.containsKey('trip_intent')) {
        aiTripIntentData = jsonMap;
      }

      processedMessage = extractionResult.cleanedMessage;
    } else {
      processedMessage = rawMessage;
    }

    return ChatSuggestionModel(
      message: processedMessage,
      suggestions: allSuggestions,
      aiTrip: aiTripData,
      aiEvent: aiEventData,
      aiList: aiListData,
      aiTripIntent: aiTripIntentData,
    );
  }

  /// Purpose-specific parsing logic
  static ChatSuggestionModel _parseWithPurpose(
      Map<String, dynamic> json, AIPurposeKey purposeKey,
      {dynamic currentTrip}) {
    switch (purposeKey) {
      case AIPurposeKey.editATrip:
        return _parseEditTripResponse(json, currentTrip: currentTrip);
      case AIPurposeKey.planATrip:
        return _parseCreateTripResponse(json);
      case AIPurposeKey.planAnEvent:
      case AIPurposeKey.editAnEvent:
        return _parseEventResponse(json);
      case AIPurposeKey.createAList:
      case AIPurposeKey.editAList:
        return _parseListResponse(json);
      case AIPurposeKey.generalPurpose:
        return _parseDefault(json);
    }
  }

  /// Parse Edit Trip responses - handles JSON Patch format (RFC 6902)
  static ChatSuggestionModel _parseEditTripResponse(Map<String, dynamic> json,
      {required dynamic currentTrip}) {
    final String rawMessage = json['message'] as String;
    List<String> allSuggestions = [];
    Map<String, dynamic>? aiTripData;
    String processedMessage = '';
    bool editingComplete = false;

    // Use unified JSON extraction method
    final extractionResult = _extractJsonFromMessage(rawMessage);

    if (extractionResult.jsonData != null) {
      final jsonMap = extractionResult.jsonData!;

      // Extract confirmation message
      if (jsonMap.containsKey('confirmation_message')) {
        processedMessage = jsonMap['confirmation_message'];
      }

      // Extract suggestions if any
      _extractSuggestions(jsonMap, allSuggestions);

      // Check for editing completion
      if (jsonMap.containsKey('status') &&
          jsonMap['status'] == 'EDITING_COMPLETE') {
        editingComplete = true;
        aiTripData = currentTrip?.toJson();
      } else if (jsonMap.containsKey('patch')) {
        // Apply JSON Patch operations (new format)
        List<dynamic> patchOperations = jsonMap['patch'];
        if (currentTrip != null && patchOperations.isNotEmpty) {
          final patchedData =
              _applyJsonPatch(currentTrip.toJson(), patchOperations);
          // For edit trip, we need data compatible with both Trip and ActivityModel
          // Use dual-compatible normalization
          aiTripData = _normalizeDataTypesForDualCompatibility(patchedData);
        } else if (patchOperations.isEmpty) {
          // Empty patch array - no changes, but confirmation_message is still valid
          // don't return a trip data if the trip is not changed
          aiTripData = null; // currentTrip?.toJson();
        }
      } else if (jsonMap.containsKey('ai_trip')) {
        // Handle legacy format for backward compatibility
        aiTripData = _normalizeDataTypesForTrip(jsonMap['ai_trip']);
      }

      // If no processedMessage was set from confirmation_message, use cleaned message
      if (processedMessage.isEmpty) {
        processedMessage = extractionResult.cleanedMessage;
      }
    } else {
      // No JSON found, use original message
      processedMessage = rawMessage;
    }

    return ChatSuggestionModel(
      message: processedMessage,
      suggestions: allSuggestions,
      aiTrip: aiTripData,
      aiEvent: null,
      aiList: null,
      aiTripIntent: null,
      editingComplete: editingComplete,
    );
  }

  /// Parse Create Trip responses - standard trip creation
  static ChatSuggestionModel _parseCreateTripResponse(
      Map<String, dynamic> json) {
    // For create trip, use the default parsing logic but with trip-specific focus
    return _parseDefault(json);
  }

  /// Parse Event responses - handles event creation and editing
  static ChatSuggestionModel _parseEventResponse(Map<String, dynamic> json) {
    // For events, use the default parsing logic but with event-specific focus
    return _parseDefault(json);
  }

  /// Parse List responses - handles list creation and editing
  static ChatSuggestionModel _parseListResponse(Map<String, dynamic> json) {
    // For lists, use the default parsing logic but with list-specific focus
    return _parseDefault(json);
  }

  /// Apply JSON Patch operations (RFC 6902) to a trip object using rfc_6902 package
  static Map<String, dynamic> _applyJsonPatch(
      Map<String, dynamic> originalData, List<dynamic> patchOperations) {
    //log the originalData
    debugPrint(
        'Applying JSON Patch to original data: ${jsonEncode(originalData)}');

    try {
      // Convert patch operations to the format expected by rfc_6902
      List<Map<String, dynamic>> patches =
          patchOperations.whereType<Map<String, dynamic>>().toList();

      // Pre-process data to ensure required paths exist
      final preprocessedData = _preprocessDataForPatches(originalData, patches);

      // Apply the JSON Patch using the rfc_6902 library
      final jsonPatch = rfc.JsonPatch(patches);
      dynamic result = jsonPatch.applyTo(preprocessedData);

      // Ensure we return a Map<String, dynamic>
      if (result is Map<String, dynamic>) {
        //PRint the full result content
        print('JSON Patch result: ${jsonEncode(result)}');

        return result;
      } else {
        if (result is Map<dynamic, dynamic>) {
          // Convert to Map<String, dynamic> if necessary
          return Map<String, dynamic>.from(result);

          //print the result content
        }

        debugPrint('JSON Patch result is not a Map, returning original data');
        return originalData;
      }
    } catch (e) {
      // Log error and return original data if patch fails
      debugPrint('Error applying JSON Patch: $e');
      return originalData;
    }
  }

  /// Pre-process data to ensure required paths exist for JSON Patch operations
  static Map<String, dynamic> _preprocessDataForPatches(
      Map<String, dynamic> data, List<Map<String, dynamic>> patches) {
    final processedData = Map<String, dynamic>.from(data);

    for (final patch in patches) {
      final op = patch['op'] as String?;
      final path = patch['path'] as String?;

      if (op == null || path == null) continue;

      // Handle operations that require existing paths
      if (op == 'add' || op == 'replace') {
        _ensurePathExists(processedData, path, op);
      }
    }

    return processedData;
  }

  /// Ensure a path exists in the data structure, creating missing parts as needed
  static void _ensurePathExists(
      Map<String, dynamic> data, String path, String operation) {
    if (!path.startsWith('/')) return;

    final pathParts = path.substring(1).split('/');
    if (pathParts.isEmpty) return;

    dynamic current = data;

    // Navigate through the path, creating missing parts
    for (int i = 0; i < pathParts.length - 1; i++) {
      final part = pathParts[i];

      if (current is Map<String, dynamic>) {
        if (!current.containsKey(part)) {
          // Determine if next part is an array index
          final nextPart = pathParts[i + 1];
          if (_isArrayIndex(nextPart)) {
            current[part] = <dynamic>[];
          } else {
            current[part] = <String, dynamic>{};
          }
        }
        current = current[part];
      } else if (current is List) {
        final index = int.tryParse(part);
        if (index != null) {
          // Expand array if necessary
          while (current.length <= index) {
            current.add(<String, dynamic>{});
          }
          current = current[index];
        }
      }
    }

    // Handle the final part of the path
    final finalPart = pathParts.last;

    if (current is Map<String, dynamic>) {
      // For itinerary array operations, ensure the array exists and has enough elements
      if (finalPart == 'itinerary' && !current.containsKey('itinerary')) {
        current['itinerary'] = <dynamic>[];
      }
    } else if (current is List) {
      final index = int.tryParse(finalPart);
      if (index != null && index != -1) {
        // -1 means append to end
        // Expand array to accommodate the index
        while (current.length <= index) {
          current.add(<String, dynamic>{
            'accommodation': '',
            'activities': <dynamic>[],
            'foodAndUrl': <String, dynamic>{},
            'imageUrl': ''
          });
        }
      }
    }
  }

  /// Check if a string represents an array index
  static bool _isArrayIndex(String part) {
    if (part == '-') return true; // RFC 6902 append notation
    final index = int.tryParse(part);
    return index != null && index >= 0;
  }

  /// Normalize data types to ensure compatibility with Trip.fromJson()
  /// Fixes type mismatches between ActivityModel.toJson() and Trip.fromJson()
  static Map<String, dynamic> _normalizeDataTypesForTrip(
      Map<String, dynamic> data) {
    final normalized = Map<String, dynamic>.from(data);

    try {
      // Fix is_date_confirmed: String -> bool
      if (normalized.containsKey('is_date_confirmed')) {
        final value = normalized['is_date_confirmed'];
        if (value is String) {
          normalized['is_date_confirmed'] = value.toLowerCase() == 'true';
        }
      }

      // Fix from_date: String? -> DateTime (required)
      if (normalized.containsKey('from_date')) {
        final value = normalized['from_date'];
        if (value is String && value.isNotEmpty) {
          // Keep as string - Trip.fromJson() will parse it
          // Just validate it's a valid date format
          try {
            DateTime.parse(value);
          } catch (e) {
            debugPrint('Invalid from_date format: $value, removing field');
            normalized.remove('from_date');
          }
        } else if (value == null || (value is String && value.isEmpty)) {
          // Remove null/empty dates as Trip requires non-null DateTime
          debugPrint('Null/empty from_date detected, removing field');
          normalized.remove('from_date');
        }
      }

      // Fix to_date: String? -> DateTime (required)
      if (normalized.containsKey('to_date')) {
        final value = normalized['to_date'];
        if (value is String && value.isNotEmpty) {
          // Keep as string - Trip.fromJson() will parse it
          // Just validate it's a valid date format
          try {
            DateTime.parse(value);
          } catch (e) {
            debugPrint('Invalid to_date format: $value, removing field');
            normalized.remove('to_date');
          }
        } else if (value == null || (value is String && value.isEmpty)) {
          // Remove null/empty dates as Trip requires non-null DateTime
          debugPrint('Null/empty to_date detected, removing field');
          normalized.remove('to_date');
        }
      }

      // Fix nested hotel_preferences date fields and type conversion
      if (normalized.containsKey('hotel_preferences') &&
          normalized['hotel_preferences'] is Map) {
        final hotelPrefs =
            _convertToStringDynamicMap(normalized['hotel_preferences'] as Map);
        normalized['hotel_preferences'] =
            _normalizeDateFieldsInPreferences(hotelPrefs);
      }

      // Fix nested flight_preferences date fields and type conversion
      if (normalized.containsKey('flight_preferences') &&
          normalized['flight_preferences'] is Map) {
        final flightPrefs =
            _convertToStringDynamicMap(normalized['flight_preferences'] as Map);
        normalized['flight_preferences'] =
            _normalizeDateFieldsInPreferences(flightPrefs);
      }

      // Fix nested budget objects in hotel/flight preferences
      _normalizeNestedBudgets(normalized);

      // Fix nested itinerary objects
      _normalizeItinerary(normalized);

      debugPrint('Data normalization completed successfully');
      return normalized;
    } catch (e) {
      debugPrint('Error during data normalization: $e');
      // Return original data if normalization fails
      return data;
    }
  }

  /// Normalize data types for dual compatibility with both Trip and ActivityModel
  /// This is a special case for edit trip responses that need to work with both models
  static Map<String, dynamic> _normalizeDataTypesForDualCompatibility(
      Map<String, dynamic> data) {
    final normalized = Map<String, dynamic>.from(data);

    try {
      // Handle is_date_confirmed: keep as String for ActivityModel compatibility
      // ChatCubit will handle the String->bool conversion for Trip.fromJson
      if (normalized.containsKey('is_date_confirmed')) {
        final value = normalized['is_date_confirmed'];
        if (value is bool) {
          normalized['is_date_confirmed'] = value.toString();
        }
        // If already String, keep as is
      }

      // Handle date fields: keep as strings (both models can handle ISO strings)
      // Trip.fromJson will parse them to DateTime, ActivityModel keeps them as String

      // Fix nested objects type conversion
      if (normalized.containsKey('hotel_preferences') &&
          normalized['hotel_preferences'] is Map) {
        final hotelPrefs =
            _convertToStringDynamicMap(normalized['hotel_preferences'] as Map);
        normalized['hotel_preferences'] =
            _normalizeDateFieldsInPreferences(hotelPrefs);
      }

      if (normalized.containsKey('flight_preferences') &&
          normalized['flight_preferences'] is Map) {
        final flightPrefs =
            _convertToStringDynamicMap(normalized['flight_preferences'] as Map);
        normalized['flight_preferences'] =
            _normalizeDateFieldsInPreferences(flightPrefs);
      }

      // Fix nested budget objects and itinerary
      _normalizeNestedBudgets(normalized);
      _normalizeItinerary(normalized);

      debugPrint(
          'Dual compatibility data normalization completed successfully');
      return normalized;
    } catch (e) {
      debugPrint('Error during dual compatibility data normalization: $e');
      return data;
    }
  }

  /// Normalize data types to ensure compatibility with ActivityModel.fromJson()
  /// Keeps data types as expected by ActivityModel (e.g., String for is_date_confirmed)
  static Map<String, dynamic> _normalizeDataTypesForActivityModel(
      Map<String, dynamic> data) {
    final normalized = Map<String, dynamic>.from(data);

    try {
      // Keep is_date_confirmed as String (ActivityModel expects String?)
      if (normalized.containsKey('is_date_confirmed')) {
        final value = normalized['is_date_confirmed'];
        if (value is bool) {
          normalized['is_date_confirmed'] = value.toString();
        } else if (value is String) {
          // Already a string, keep as is
        }
      }

      // Keep date fields as String (ActivityModel expects String?)
      // from_date and to_date should remain as ISO strings

      // Fix nested objects type conversion (same as Trip normalization)
      if (normalized.containsKey('hotel_preferences') &&
          normalized['hotel_preferences'] is Map) {
        final hotelPrefs =
            _convertToStringDynamicMap(normalized['hotel_preferences'] as Map);
        normalized['hotel_preferences'] =
            _normalizeDateFieldsInPreferences(hotelPrefs);
      }

      if (normalized.containsKey('flight_preferences') &&
          normalized['flight_preferences'] is Map) {
        final flightPrefs =
            _convertToStringDynamicMap(normalized['flight_preferences'] as Map);
        normalized['flight_preferences'] =
            _normalizeDateFieldsInPreferences(flightPrefs);
      }

      // Fix nested budget objects and itinerary
      _normalizeNestedBudgets(normalized);
      _normalizeItinerary(normalized);

      debugPrint('ActivityModel data normalization completed successfully');
      return normalized;
    } catch (e) {
      debugPrint('Error during ActivityModel data normalization: $e');
      return data;
    }
  }

  /// Normalize date fields in hotel/flight preferences
  static Map<String, dynamic> _normalizeDateFieldsInPreferences(
      Map<String, dynamic> preferences) {
    final normalized = Map<String, dynamic>.from(preferences);

    // List of date fields that need normalization in preferences
    final dateFields = [
      'check_in_date',
      'check_out_date',
      'departure_date',
      'return_date'
    ];

    for (final field in dateFields) {
      if (normalized.containsKey(field)) {
        final value = normalized[field];
        if (value == null || (value is String && value.isEmpty)) {
          // Remove null/empty dates
          debugPrint('Null/empty $field detected, removing field');
          normalized.remove(field);
        } else if (value is String && value.isNotEmpty) {
          // Validate date format
          try {
            DateTime.parse(value);
            // Keep as string - the fromJson() will parse it
          } catch (e) {
            debugPrint('Invalid $field format: $value, removing field');
            normalized.remove(field);
          }
        }
      }
    }

    return normalized;
  }

  /// Convert Map<dynamic, dynamic> to Map<String, dynamic>
  static Map<String, dynamic> _convertToStringDynamicMap(Map map) {
    final result = <String, dynamic>{};
    map.forEach((key, value) {
      final stringKey = key.toString();
      if (value is Map) {
        result[stringKey] = _convertToStringDynamicMap(value);
      } else if (value is List) {
        result[stringKey] = _convertListItems(value);
      } else {
        result[stringKey] = value;
      }
    });
    return result;
  }

  /// Convert list items, handling nested maps
  static List<dynamic> _convertListItems(List list) {
    return list.map((item) {
      if (item is Map) {
        return _convertToStringDynamicMap(item);
      } else if (item is List) {
        return _convertListItems(item);
      } else {
        return item;
      }
    }).toList();
  }

  /// Normalize nested budget objects in hotel/flight preferences
  static void _normalizeNestedBudgets(Map<String, dynamic> normalized) {
    // Fix budget in hotel_preferences
    if (normalized.containsKey('hotel_preferences') &&
        normalized['hotel_preferences'] is Map<String, dynamic>) {
      final hotelPrefs =
          normalized['hotel_preferences'] as Map<String, dynamic>;
      if (hotelPrefs.containsKey('budget') && hotelPrefs['budget'] is Map) {
        hotelPrefs['budget'] =
            _convertToStringDynamicMap(hotelPrefs['budget'] as Map);
      }
    }

    // Fix budget in flight_preferences
    if (normalized.containsKey('flight_preferences') &&
        normalized['flight_preferences'] is Map<String, dynamic>) {
      final flightPrefs =
          normalized['flight_preferences'] as Map<String, dynamic>;
      if (flightPrefs.containsKey('budget') && flightPrefs['budget'] is Map) {
        flightPrefs['budget'] =
            _convertToStringDynamicMap(flightPrefs['budget'] as Map);
      }
    }
  }

  /// Normalize itinerary array and nested objects
  static void _normalizeItinerary(Map<String, dynamic> normalized) {
    if (normalized.containsKey('itinerary') &&
        normalized['itinerary'] is List) {
      final itinerary = normalized['itinerary'] as List;
      for (int i = 0; i < itinerary.length; i++) {
        if (itinerary[i] is Map) {
          itinerary[i] = _convertToStringDynamicMap(itinerary[i] as Map);

          // Normalize nested activities
          final dayData = itinerary[i] as Map<String, dynamic>;
          if (dayData.containsKey('activities') &&
              dayData['activities'] is List) {
            final activities = dayData['activities'] as List;
            for (int j = 0; j < activities.length; j++) {
              if (activities[j] is Map) {
                activities[j] =
                    _convertToStringDynamicMap(activities[j] as Map);
              }
            }
          }

          // Normalize nested transfers
          if (dayData.containsKey('transfers') &&
              dayData['transfers'] is List) {
            final transfers = dayData['transfers'] as List;
            for (int j = 0; j < transfers.length; j++) {
              if (transfers[j] is Map) {
                transfers[j] = _convertToStringDynamicMap(transfers[j] as Map);
              }
            }
          }

          // Normalize foodAndUrl map
          if (dayData.containsKey('foodAndUrl') &&
              dayData['foodAndUrl'] is Map) {
            dayData['foodAndUrl'] =
                _convertToStringDynamicMap(dayData['foodAndUrl'] as Map);
          }
        }
      }
    }
  }

  /// Unified method to extract JSON from various message formats
  static _JsonExtractionResult _extractJsonFromMessage(String rawMessage) {
    final trimmedMessage = rawMessage.trim();

    // Case 1: Check if the entire message is pure JSON
    if (_isPureJson(trimmedMessage)) {
      try {
        final jsonData = jsonDecode(trimmedMessage);
        if (jsonData is Map<String, dynamic>) {
          return _JsonExtractionResult(
            jsonData: jsonData,
            cleanedMessage: '', // Pure JSON has no additional message
            isPureJson: true,
          );
        }
      } catch (e) {
        // If pure JSON parsing fails, fall through to other methods
      }
    }

    // Case 2: Handle JSON in markdown code blocks
    if (rawMessage.contains("```json") && rawMessage.contains("```")) {
      return _extractJsonFromCodeBlock(rawMessage);
    }

    // Case 3: Handle inline JSON within text
    return _extractInlineJson(rawMessage);
  }

  /// Check if a string is pure JSON (starts with { and ends with })
  static bool _isPureJson(String message) {
    return message.startsWith('{') && message.endsWith('}');
  }

  /// Extract JSON from markdown code blocks
  static _JsonExtractionResult _extractJsonFromCodeBlock(String rawMessage) {
    final startIndex = rawMessage.indexOf("```json");
    final endIndex = rawMessage.lastIndexOf("```") + 3;
    final jsonBlock = rawMessage
        .substring(startIndex, endIndex)
        .replaceAll("```json", "")
        .replaceAll("```", "")
        .trim();

    try {
      final jsonData = jsonDecode(jsonBlock);
      if (jsonData is Map<String, dynamic>) {
        final cleanedMessage =
            rawMessage.replaceRange(startIndex, endIndex, "").trim();
        return _JsonExtractionResult(
          jsonData: jsonData,
          cleanedMessage: cleanedMessage,
        );
      }
    } catch (e) {
      // If JSON parsing fails, return original message
    }

    return _JsonExtractionResult(cleanedMessage: rawMessage);
  }

  /// Extract inline JSON from text lines
  static _JsonExtractionResult _extractInlineJson(String rawMessage) {
    final lines = rawMessage.split('\n');
    List<String> processedLines = [];
    Map<String, dynamic> mergedJson = {};

    for (String line in lines) {
      String processedLine = line;
      int startIndex = line.indexOf('{');

      while (startIndex != -1) {
        int endIndex = -1;
        int braceCount = 0;

        // Find the matching closing brace
        for (int i = startIndex; i < line.length; i++) {
          if (line[i] == '{') braceCount++;
          if (line[i] == '}') braceCount--;
          if (braceCount == 0) {
            endIndex = i + 1;
            break;
          }
        }

        if (endIndex != -1) {
          try {
            final jsonStr = line.substring(startIndex, endIndex);
            final jsonData = jsonDecode(jsonStr);
            if (jsonData is Map<String, dynamic>) {
              // Merge all found JSON objects
              _mergeJsonData(mergedJson, jsonData);
              processedLine = processedLine.replaceFirst(jsonStr, '').trim();
            }
          } catch (e) {
            // If JSON parsing fails, keep the line as is
          }
        }

        startIndex = line.indexOf('{', startIndex + 1);
      }

      if (processedLine.isNotEmpty) {
        processedLines.add(processedLine);
      }
    }

    return _JsonExtractionResult(
      jsonData: mergedJson.isNotEmpty ? mergedJson : null,
      cleanedMessage: processedLines.join('\n').trim(),
    );
  }

  /// Merge JSON data, combining arrays and preserving other fields
  static void _mergeJsonData(
      Map<String, dynamic> target, Map<String, dynamic> source) {
    source.forEach((key, value) {
      if (target.containsKey(key)) {
        // If both have the same key, handle merging
        if (key == 'suggestion' && target[key] is List && value is List) {
          // Merge suggestion arrays
          (target[key] as List).addAll(value);
        } else {
          // For other fields, source overwrites target
          target[key] = value;
        }
      } else {
        // New key, just add it
        target[key] = value;
      }
    });
  }

  static void _extractSuggestions(dynamic jsonMap, List<String> suggestions) {
    if (jsonMap is Map<String, dynamic>) {
      jsonMap.forEach((key, value) {
        if (value is Map<String, dynamic>) {
          // Handle nested categories like "cities" and "interests"
          if (value.containsKey('suggestion')) {
            suggestions.addAll(List<String>.from(value['suggestion']));
          }
        } else if (key == 'suggestion' && value is List) {
          // Handle direct suggestion array
          suggestions.addAll(List<String>.from(value));
        }
      });
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'message': message,
      'suggestions': suggestions,
      if (aiTrip != null) 'ai_trip': aiTrip,
      if (aiEvent != null) 'ai_event': aiEvent,
      if (aiList != null) 'ai_list': aiList,
      if (aiTripIntent != null) 'ai_trip_intent': aiTripIntent,
      if (editingComplete != null) 'editing_complete': editingComplete,
    };
  }
}
