import 'dart:convert';

import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/data/model/account.dart';
import 'package:family_app/data/model/category.dart';
import 'package:family_app/data/model/event.dart';
import 'package:family_app/data/model/hotel_booking_model.dart';
import 'package:family_app/data/model/list_item.dart';
import 'package:family_app/data/model/trip_model.dart';
import 'package:family_app/data/usecase/model/activity_parameter.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:json_annotation/json_annotation.dart';

part 'activity.g.dart';

@JsonSerializable(explicitToJson: true)
class ActivityModel {
  @Json<PERSON>ey(name: 'uuid')
  String uuid;
  int? status;
  @JsonKey(name: 'created_at')
  String? createdAt;
  @JsonKey(name: 'created_by')
  String? createdBy;
  @Json<PERSON>ey(name: 'family_id')
  String? familyId;
  @Json<PERSON>ey(name: 'activity_type')
  String? activityType;
  @Json<PERSON>ey(name: 'name')
  String? name;
  @Json<PERSON>ey(name: 'description')
  String? description;
  @Json<PERSON><PERSON>(name: 'is_date_confirmed')
  String? isDateConfirmed;
  @JsonKey(name: 'country')
  String? country;
  @JsonKey(name: 'city')
  String? city;
  @JsonKey(name: 'from_date')
  String? fromDate;
  @JsonKey(name: 'to_date')
  String? toDate;
  String? color;
  String? caption;
  String? imagePath;
  String? abbreviation;
  int? trip_duration; // in days

  @JsonKey(name: 'included_members', fromJson: _fromJsonAccountList)
  List<Account>? includedMembers;

  @JsonKey(name: 'included_events', fromJson: _fromJsonEventList)
  List<EventModels>? includedEvents;

  @JsonKey(name: 'included_lists', fromJson: _fromJsonListItemList)
  List<ListItem>? includedLists;

  Category? type;
  @JsonKey(name: 'list_uuid')
  String? listUuid;

  @JsonKey(name: 'itinerary', fromJson: _fromJsonItineraryList)
  List<Itinerary>? itinerary;

  @JsonKey(name: 'hotel_preferences')
  HotelPreferences? hotelPreferences;

  @JsonKey(name: 'flight_preferences')
  FlightPreferences? flightPreferences;

  @JsonKey(name: 'city_code')
  String? cityCode;

  @JsonKey(name: 'additional_cities')
  List<String>? additionalCities;

  @JsonKey(name: 'additional_city_code')
  List<String>? additionalCityCode;

  @JsonKey(name: 'attachments')
  final List<dynamic>? attachments;

  @JsonKey(name: 'hotel_bookings', fromJson: _parseHotelBookings)
  final List<HotelBookingModel>? hotelBookings;

  ActivityModel({
    required this.uuid,
    this.status,
    this.createdAt,
    this.createdBy,
    this.familyId,
    this.activityType,
    this.name,
    this.description,
    this.isDateConfirmed,
    this.country,
    this.city,
    this.fromDate,
    this.toDate,
    this.color,
    this.caption,
    this.imagePath,
    this.abbreviation,
    this.includedMembers,
    this.includedEvents,
    this.includedLists,
    this.type,
    this.listUuid,
    this.itinerary,
    this.attachments,
    this.hotelPreferences,
    this.flightPreferences,
    this.cityCode,
    this.additionalCities,
    this.additionalCityCode,
    this.hotelBookings,
  });

  factory ActivityModel.fromJson(Map<String, dynamic> json) => _$ActivityModelFromJson(json);

  Map<String, dynamic> toJson() => _$ActivityModelToJson(this);

  ActivityModel copyWith({
    String? uuid,
    int? status,
    String? createdAt,
    String? createdBy,
    String? familyId,
    String? activityType,
    String? name,
    String? description,
    String? isDateConfirmed,
    String? country,
    String? city,
    String? fromDate,
    String? toDate,
    String? color,
    String? caption,
    String? imagePath,
    String? abbreviation,
    List<Account>? includedMembers,
    List<EventModels>? includedEvents,
    List<ListItem>? includedLists,
    List<Itinerary>? itinerary,
    List<dynamic>? attachments,
    Category? type,
    HotelPreferences? hotelPreferences,
    FlightPreferences? flightPreferences,
    String? cityCode,
    List<String>? additionalCities,
    List<String>? additionalCityCode,
    List<HotelBookingModel>? hotelBookings,
  }) {
    return ActivityModel(
      uuid: uuid ?? this.uuid,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      familyId: familyId ?? this.familyId,
      activityType: activityType ?? this.activityType,
      name: name ?? this.name,
      description: description ?? this.description,
      isDateConfirmed: isDateConfirmed ?? this.isDateConfirmed,
      country: country ?? this.country,
      city: city ?? this.city,
      fromDate: fromDate ?? this.fromDate,
      toDate: toDate ?? this.toDate,
      color: color ?? this.color,
      caption: caption ?? this.caption,
      imagePath: imagePath ?? this.imagePath,
      abbreviation: abbreviation ?? this.abbreviation,
      includedMembers: includedMembers ?? this.includedMembers,
      includedEvents: includedEvents ?? this.includedEvents,
      includedLists: includedLists ?? this.includedLists,
      itinerary: itinerary ?? this.itinerary,
      attachments: attachments ?? this.attachments,
      type: type ?? this.type,
      hotelPreferences: hotelPreferences ?? this.hotelPreferences,
      flightPreferences: flightPreferences ?? this.flightPreferences,
      cityCode: cityCode ?? this.cityCode,
      additionalCities: additionalCities ?? this.additionalCities,
      additionalCityCode: additionalCityCode ?? this.additionalCityCode,
      hotelBookings: hotelBookings ?? this.hotelBookings,
    );
  }

  static _fromJsonAccountList(dynamic json) {
    if (json == null || (json is String && json.isEmpty)) {
      return <Account>[];
    }

    if (json is String) {
      List<String> ids = json.split(',');
      return ids.map((id) => Account(familyMemberUuid: id, uuid: id)).toList();
    }

    return (json as List<dynamic>).map((e) {
      var res = Account.fromJson(e as Map<String, dynamic>);
      res.familyMemberUuid ??= res.uuid;
      return res;
    }).toList();
  }

  static _fromJsonEventList(dynamic json) {
    if (json == null || (json is String && json.isEmpty)) {
      return <EventModels>[];
    }

    if (json is String) {
      List<String> ids = json.split(',');
      return ids.map((id) => EventModels()).toList();
    }

    return (json as List<dynamic>).map((e) => EventModels.fromJson(e as Map<String, dynamic>)).toList();
  }

  static _fromJsonListItemList(dynamic json) {
    if (json == null || (json is String && json.isEmpty)) {
      return <ListItem>[];
    }

    if (json is String) {
      List<String> ids = json.split(',');
      return ids.map((id) => ListItem()).toList();
    }

    return (json as List<dynamic>).map((e) => ListItem.fromJson(e as Map<String, dynamic>)).toList();
  }

  static _fromJsonItineraryList(dynamic json) {
    if (json == null || (json is String && json.isEmpty)) {
      return <Itinerary>[];
    }

    if (json is String) {
      List<String> ids = json.split(',');
      return ids.map((id) => Itinerary()).toList();
    }

    if (json is List<dynamic>) {
      return json.map((e) => Itinerary.fromJson(e as Map<String, dynamic>)).toList();
    }

    return <Itinerary>[];
  }

  @override
  String toString() {
    return 'ActivityModel(uuid: $uuid,  createdAt: $createdAt, createdBy: $createdBy, activityType: $activityType, name: $name, description: $description, isDateConfirmed: $isDateConfirmed, country: $country, city: $city, fromDate: $fromDate, toDate: $toDate, type: $type, listUuid: $listUuid)';
  }

//return the duration in this format "13 Jan - 14 Jan"
  String shortFromTo() {
    final DateFormat formatter = DateFormat('dd MMM');

    if (fromDate != null && toDate != null) {
      DateTime from = fromDate!.toLocalDT;
      DateTime to = toDate!.toLocalDT;

      return '${formatter.format(from)} - ${formatter.format(to)}';
    }
    return '';
  }

  String shortFrom() {
    final DateFormat formatter = DateFormat('dd MMM');

    if (fromDate != null) {
      DateTime from = fromDate!.toLocalDT;

      return '${formatter.format(from)}}';
    }
    return '';
  }

  /// Helper method to safely retrieve the image path from attachments
  String? getImagePath() {
    if (attachments is! List) {
      return null;
    }
    final files = (attachments?.firstOrNull as Map<String, dynamic>?)?['files'] as List<dynamic>?;
    return (files?.firstOrNull as Map<String, dynamic>?)?['file_url_md'] as String?;
  }

  CreateActivityParameter toCreateActivityParameter() {
    var activity = this;
    final isDateConfirmed =
        activity.isDateConfirmed != null ? (activity.isDateConfirmed == 'true' ? true : false) : null;
    List<String>? hotelBookings = activity.hotelBookings?.map((e) => jsonEncode(e.toJson())).toList();
    return CreateActivityParameter(
      name: activity.name!,
      isDateConfirmed: isDateConfirmed,
      fromDate: activity.fromDate!,
      toDate: activity.toDate!,
      caption: activity.caption,
      country: activity.country,
      city: activity.city,
      color: activity.color,
      description: activity.description,
      includedEvents: activity.includedEvents,
      itinerary: activity.itinerary!,
      activityType: activity.activityType!,
      familyId: activity.familyId!,
      uuid: activity.uuid,
      tripImageUrl: activity.imagePath,
      hotelBookings: hotelBookings,
    );
  }

  static _parseHotelBookings(dynamic json) {
    if (json == null || (json is String && json.isEmpty)) {
      return <HotelBookingModel>[];
    }
    if (json is String) {
      return [];
    }
    if (json is List) {
      return json.map((e) => HotelBookingModel.fromJson(jsonDecode(e))).toList();
    }
    return [];
  }
}
