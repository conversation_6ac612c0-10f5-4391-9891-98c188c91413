import 'package:flutter_test/flutter_test.dart';
import 'package:family_app/data/model/chat_suggestion_model.dart';
import 'package:family_app/screen/main/chat/chat_context.dart';

void main() {
  group('ChatSuggestionModel Tests', () {
    test('should parse simple suggestion array', () {
      final json = {
        'message':
            'Choose a city: {"suggestion": ["New York", "London", "Paris"]}',
      };

      final model = ChatSuggestionModel.fromJson(json);

      expect(model.suggestions, equals(['New York', 'London', 'Paris']));
      expect(model.message, equals('Choose a city:'));
    });

    test('should parse nested category structure', () {
      final json = {
        'message': '''
Choose your preferences:
```json
{
  "cities": {
    "suggestion": ["Tokyo", "Kyoto", "Osaka"]
  },
  "interests": {
    "suggestion": ["Culture", "Food", "Shopping"]
  }
}
```
''',
      };

      final model = ChatSuggestionModel.fromJson(json);

      expect(model.suggestions,
          equals(['Tokyo', 'Kyoto', 'Osaka', 'Culture', 'Food', 'Shopping']));
      expect(model.message, contains('Choose your preferences:'));
    });

    test('should parse multiple inline JSON objects', () {
      final json = {
        'message': '''
1. Select a destination: {"suggestion": ["Bali", "Phuket", "Bangkok"]}
2. Choose activities: {"suggestion": ["Beach", "Temples", "Markets"]}
''',
      };

      final model = ChatSuggestionModel.fromJson(json);

      expect(model.suggestions,
          equals(['Bali', 'Phuket', 'Bangkok', 'Beach', 'Temples', 'Markets']));
      expect(model.message, contains('1. Select a destination:'));
      expect(model.message, contains('2. Choose activities:'));
    });

    //SKIP this test for now
//     test('should handle mixed JSON formats', () {
//       final json = {
//         'message': '''
// Let's plan your trip:

// 1. Choose a city: {"suggestion": ["Rome", "Florence", "Venice"]}

// 2. Select your preferences:
// ```json
// {
//   "activities": {
//     "suggestion": ["Museums", "Food Tours", "Shopping"]
//   },
//   "accommodation": {
//     "suggestion": ["Hotel", "Airbnb", "Hostel"]
//   }
// }
// ```
// ''',
//       };

//       final model = ChatSuggestionModel.fromJson(json);

//       expect(model.suggestions,
//         equals(['Rome', 'Florence', 'Venice', 'Museums', 'Food Tours', 'Shopping', 'Hotel', 'Airbnb', 'Hostel']));
//       expect(model.message, contains('Let\'s plan your trip:'));
//     });

    test('should handle invalid JSON gracefully', () {
      final json = {
        'message': '''
Invalid JSON test:
{"suggestion": ["Item1", "Item2" // Missing closing bracket
''',
      };

      final model = ChatSuggestionModel.fromJson(json);

      expect(model.message, contains('Invalid JSON test:'));
      expect(model.suggestions, isEmpty);
    });

    test('should parse ai_trip data from JSON block', () {
      final json = {
        'message': '''
Here's your trip plan:
```json
{
  "ai_trip": {
    "name": "Relaxing Family Trip to Moscow",
    "country": "Russia",
    "city": "Moscow",
    "is_date_confirmed": false,
    "from_date": "2025-06-01T00:00:00.000Z",
    "to_date": "2025-06-07T00:00:00.000Z",
    "color": "#133483",
    "description": "Relaxing family trip to explore Moscow's history, culture and nightlife",
    "included_events": [],
    "activity_type": "23042438-c43a-4722-8e5f-2999d64df723",
    "tripImageUrl": "url-trip-place-holder",
    "hotel_preferences": {
      "location": "City Center",
      "check_in_date": "2025-06-01T00:00:00.000Z",
      "check_out_date": "2025-06-08T00:00:00.000Z"
    },
    "number_of_guests": 4,
    "room_type": "Family Suite",
    "star_rating": 4,
    "amenities": ["Pool", "Wifi", "Breakfast"],
    "budget": {
      "min": 1000,
      "max": 2000
    },
    "flight_preferences": {
      "departure_location": "Your Location",
      "arrival_location": "Moscow (MOW)",
      "departure_date": "2025-06-01T00:00:00.000Z",
      "return_date": "2025-06-08T00:00:00.000Z",
      "number_of_passengers": 4,
      "cabin_class": "Economy",
      "preferred_airlines": [],
      "budget": {
        "min": 800,
        "max": 1600
      }
    },
    "itinerary": [
      {
        "accommodation": "Hotel Name",
        "imageUrl": "url-activity-place-holder",
        "activities": [
          {
            "time": "AM",
            "description": "Check into hotel and relax",
            "venue": "Hotel",
            "city": "Moscow"
          },
          {
            "time": "PM",
            "description": "Evening stroll through Red Square",
            "venue": "Red Square",
            "city": "Moscow"
          }
        ],
        "foodAndUrl": {
          "Food item 1": "url-food-placeholder",
          "Food item 2": "url-food-placeholder"
        }
      }
    ]
  }
}
```
''',
      };

      final model = ChatSuggestionModel.fromJson(json);

      expect(model.aiTrip, isNotNull);
      expect(model.aiTrip!['name'], equals('Relaxing Family Trip to Moscow'));
      expect(model.aiTrip!['country'], equals('Russia'));
      expect(model.aiTrip!['city'], equals('Moscow'));
      expect(model.aiTrip!['is_date_confirmed'], isFalse);
      expect(model.aiTrip!['color'], equals('#133483'));
      expect(model.aiTrip!['number_of_guests'], equals(4));
      expect(model.aiTrip!['star_rating'], equals(4));

      // Test nested objects
      expect(model.aiTrip!['hotel_preferences']['location'],
          equals('City Center'));
      expect(model.aiTrip!['budget']['min'], equals(1000));
      expect(model.aiTrip!['budget']['max'], equals(2000));

      // Test arrays
      expect(model.aiTrip!['amenities'], equals(['Pool', 'Wifi', 'Breakfast']));
      expect(model.aiTrip!['included_events'], isEmpty);

      // Test flight preferences
      expect(model.aiTrip!['flight_preferences']['cabin_class'],
          equals('Economy'));
      expect(model.aiTrip!['flight_preferences']['number_of_passengers'],
          equals(4));

      // Test itinerary
      expect(model.aiTrip!['itinerary'].length, equals(1));
      expect(
          model.aiTrip!['itinerary'][0]['accommodation'], equals('Hotel Name'));
      expect(model.aiTrip!['itinerary'][0]['activities'].length, equals(2));

      expect(model.message, contains('Here\'s your trip plan:'));
    });

    test('should parse both suggestions and ai_trip data', () {
      final json = {
        'message': '''
Let's plan your trip:
```json
{
  "suggestion": ["Moscow", "St. Petersburg", "Sochi"],
  "ai_trip": {
    "name": "Russian Adventure",
    "country": "Russia",
    "city": "Moscow",
    "is_date_confirmed": true
  }
}
```
''',
      };

      final model = ChatSuggestionModel.fromJson(json);

      expect(model.suggestions, equals(['Moscow', 'St. Petersburg', 'Sochi']));
      expect(model.aiTrip, isNotNull);
      expect(model.aiTrip!['name'], equals('Russian Adventure'));
      expect(model.aiTrip!['country'], equals('Russia'));
      expect(model.message, contains('Let\'s plan your trip:'));
    });

    test('should handle missing ai_trip data', () {
      final json = {
        'message':
            'Choose a city: {"suggestion": ["New York", "London", "Paris"]}',
      };

      final model = ChatSuggestionModel.fromJson(json);

      expect(model.suggestions, equals(['New York', 'London', 'Paris']));
      expect(model.aiTrip, isNull);
      expect(model.message, equals('Choose a city:'));
    });

    test('should handle invalid ai_trip data gracefully', () {
      final json = {
        'message': '''
Invalid trip data:
```json
{
  "ai_trip": {
    "name": "Invalid Trip",
    // Missing closing brace
}
```
''',
      };

      final model = ChatSuggestionModel.fromJson(json);

      expect(model.aiTrip, isNull);
      expect(model.message, contains('Invalid trip data:'));
    });

    test('should parse ai_event data from JSON block', () {
      final json = {
        'message': '''
Here's your event:
```json
{
  "ai_event": {
    "name": "Family Dinner",
    "date": "2024-03-20T18:00:00.000Z",
    "location": "Italian Restaurant",
    "participants": ["John", "Jane", "Kids"],
    "type": "dinner"
  }
}
```
''',
      };

      final model = ChatSuggestionModel.fromJson(json);

      expect(model.aiEvent, isNotNull);
      expect(model.aiEvent!['name'], equals('Family Dinner'));
      expect(model.aiEvent!['location'], equals('Italian Restaurant'));
      expect(model.aiEvent!['type'], equals('dinner'));
      expect(model.message, contains('Here\'s your event:'));
    });

    test('should parse ai_list data from JSON block', () {
      final json = {
        'message': '''
Here's your shopping list:
```json
{
  "ai_list": {
    "name": "Grocery Shopping",
    "items": [
      {"name": "Milk", "quantity": "1 gallon"},
      {"name": "Bread", "quantity": "2 loaves"},
      {"name": "Eggs", "quantity": "1 dozen"}
    ],
    "store": "Local Market",
    "priority": "high"
  }
}
```
''',
      };

      final model = ChatSuggestionModel.fromJson(json);

      expect(model.aiList, isNotNull);
      expect(model.aiList!['name'], equals('Grocery Shopping'));
      expect(model.aiList!['store'], equals('Local Market'));
      expect(model.aiList!['priority'], equals('high'));
      expect((model.aiList!['items'] as List).length, equals(3));
      expect(model.message, contains('Here\'s your shopping list:'));
    });

    test('should parse multiple AI data types together', () {
      final json = {
        'message': '''
Complete plan:
```json
{
  "suggestion": ["Option 1", "Option 2"],
  "ai_trip": {
    "name": "Weekend Getaway",
    "destination": "Beach Resort"
  },
  "ai_event": {
    "name": "Beach Party",
    "time": "14:00"
  },
  "ai_list": {
    "name": "Packing List",
    "items": ["Swimsuit", "Sunscreen"]
  }
}
```
''',
      };

      final model = ChatSuggestionModel.fromJson(json);

      expect(model.suggestions, equals(['Option 1', 'Option 2']));
      expect(model.aiTrip, isNotNull);
      expect(model.aiEvent, isNotNull);
      expect(model.aiList, isNotNull);

      expect(model.aiTrip!['name'], equals('Weekend Getaway'));
      expect(model.aiEvent!['name'], equals('Beach Party'));
      expect(model.aiList!['name'], equals('Packing List'));

      expect(model.message, contains('Complete plan:'));
    });

    test('should handle missing AI data fields', () {
      final json = {
        'message': 'Simple message: {"suggestion": ["Item1", "Item2"]}',
      };

      final model = ChatSuggestionModel.fromJson(json);

      expect(model.suggestions, equals(['Item1', 'Item2']));
      expect(model.aiTrip, isNull);
      expect(model.aiEvent, isNull);
      expect(model.aiList, isNull);
      expect(model.message, equals('Simple message:'));
    });

    test('should handle invalid AI data gracefully', () {
      final json = {
        'message': '''
Invalid data:
```json
{
  "ai_event": {
    "name": "Invalid Event",
    // Missing closing brace
  },
  "ai_list": {
    "name": "Invalid List"
    // Missing closing brace
}
```
''',
      };

      final model = ChatSuggestionModel.fromJson(json);

      expect(model.aiEvent, isNull);
      expect(model.aiList, isNull);
      expect(model.message, contains('Invalid data:'));
    });

    test('should handle purpose-aware parsing for edit trip', () {
      final json = {
        'message': '''
Edit trip response:
```json
{
  "ai_trip": {
    "name": "Updated Trip to Paris",
    "city": "Paris",
    "modifications": ["Changed hotel", "Added restaurant"]
  }
}
```
''',
      };

      final model = ChatSuggestionModel.fromJson(json,
          purposeKey: AIPurposeKey.editATrip);

      expect(model.aiTrip, isNotNull);
      expect(model.aiTrip!['name'], equals('Updated Trip to Paris'));
      expect(model.aiTrip!['city'], equals('Paris'));
      expect(model.message, contains('Edit trip response:'));
    });

    test('should handle purpose-aware parsing for general purpose', () {
      final json = {
        'message': 'General response: {"suggestion": ["Option 1", "Option 2"]}',
      };

      final model = ChatSuggestionModel.fromJson(json,
          purposeKey: AIPurposeKey.generalPurpose);

      expect(model.suggestions, equals(['Option 1', 'Option 2']));
      expect(model.aiTrip, isNull);
      expect(model.message, equals('General response:'));
    });

    test('should fallback to default parsing when no purpose key provided', () {
      final json = {
        'message': 'Default parsing: {"suggestion": ["Item 1", "Item 2"]}',
      };

      final model = ChatSuggestionModel.fromJson(json);

      expect(model.suggestions, equals(['Item 1', 'Item 2']));
      expect(model.message, equals('Default parsing:'));
    });
  });
}
