#!/bin/sh 

# if $1 is '-h' or '--help' then print the help message
if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
  echo "Usage: $0 [options]"
  echo "Options:"
  echo "  -a        Skip iOS build, just prepare the build number and push to the remote branch for Android auto build"
  echo "  -b         Increment build number"
  echo "  -bv         Increment version number & build number"
  echo "  -i    just build local iOS app"
  echo "  -h, --help Show this help message"
  echo "  -t        Run unit test"
  echo "  xx    others are ignored"
  exit 0
fi

######
###### Runner 
######

function gen_runner() {
  dart run build_runner build  && flutter pub  run easy_localization:generate -O lib/config/lang -f keys -o locale_keys.g.dart --source-dir ./assets/lang 

 #if above fails, exit the script
  if [ $? -ne 0 ]; then
    echo "Failed to generate output from Runner"
    exit 1
  fi

}



########################################################
######### UNIT TEST  #########
########################################################

function run_unit_test() {
#run the unit test
 flutter test test/data/model/chat_suggestion_model_test.dart || (echo "Unit test failed" && exit 1)

 # Run all form validation tests
flutter test test/form_validation/
}






########################################################
######### QUICK LOCAL BUILD #########
########################################################

#  
#[1]: Nguyen Tri Phung’s iPhone (00008020-001468583641402E)
#[2]: iPhone 16 (177E9FBE-9ACA-4B07-815C-BD2BCBE94906)
#[3]: Mac Designed for iPad (mac-designed-for-ipad)## 
#
# quick logic to select the device to run the app, if there is a real iphone, run on it, else run on the simulator

#function flutter_run() {
#  if [ -n "$1" ]; then
#    echo "Running on device: $1"
#    flutter run -d $1
#  else
#    echo "Running on simulator"
#    flutter run
#  fi
#}






#no arg, just build without runner , the local app
if [ $# -eq 0 ]; then
  echo "Running local iOS app..."
  flutter run 
  exit 0
fi

if [ "$1" = "-i" ]; then

   echo "Building local iOS app..."
  gen_runner

  flutter run
  exit 0
fi

if [ "$1" = "-t" ]; then

  gen_runner

  echo "Running unit test..."
  run_unit_test

  exit 0
fi








########################################################
######### FULL BUILD #########
########################################################
#read the parameters, if -a is passed, then run skip the IPA build , and run only Android build
SHOULD_BUILD_IOS=1
if [ "$1" = "-a" ]; then
  echo "Skipping iOS build..."
  SHOULD_BUILD_IOS=0
fi




# Define the branch to build from
BUILD_BRANCH="develop"
APK_BUILD_BRANCH="auto_build"

# Check if the current branch matches the build branch
CURRENT_BRANCH=$(git rev-parse --abbrev-ref HEAD)
if [ "$CURRENT_BRANCH" != "$BUILD_BRANCH" ]; then
  echo "You are currently on branch '$CURRENT_BRANCH'. Please switch to the '$BUILD_BRANCH' branch to proceed."
  exit 1
fi


#Check if there is a newer version of the build branch on the remote
git fetch origin $BUILD_BRANCH
LOCAL_COMMIT=$(git rev-parse HEAD)
REMOTE_COMMIT=$(git rev-parse origin/$BUILD_BRANCH)

if [ "$LOCAL_COMMIT" != "$REMOTE_COMMIT" ]; then
  LOCAL_COMMIT_TIME=$(git show -s --format=%ct $LOCAL_COMMIT)
  REMOTE_COMMIT_TIME=$(git show -s --format=%ct $REMOTE_COMMIT)

  if [ "$LOCAL_COMMIT_TIME" -gt "$REMOTE_COMMIT_TIME" ]; then
    echo "Your local '$BUILD_BRANCH' branch has newer commits than the remote."
    read -p "Would you like to push your local changes to the remote branch? (y/n): " PUSH_CONFIRM
    if [ "$PUSH_CONFIRM" = "y" ]; then
      git push origin $BUILD_BRANCH || (echo "Failed to push local changes to the remote branch. Please resolve any issues and try again." && exit 1)
      echo "Successfully pushed local changes to the remote '$BUILD_BRANCH' branch."
    else
      echo "Proceeding without pushing local changes to the remote branch."
    fi
  else
    echo "There is a newer version of the '$BUILD_BRANCH' branch on the remote."
    read -p "Would you like to sync with the remote branch before building? (y/n): " SYNC_CONFIRM
    if [ "$SYNC_CONFIRM" = "y" ]; then
      git pull origin $BUILD_BRANCH || (echo "Failed to sync with the remote branch. Please resolve any conflicts and try again." && exit 1)
      echo "Successfully synced with the remote '$BUILD_BRANCH' branch."
    else
      echo "Proceeding without syncing with the remote branch."
    fi
  fi
else
  echo "Your local '$BUILD_BRANCH' branch is up to date with the remote."
fi


# Get version and build number from pubspec.yaml
VERSION=$(grep '^version:' pubspec.yaml | awk '{print $2}' | cut -d '+' -f1)
BUILD_NUMBER=$(grep '^version:' pubspec.yaml | awk '{print $2}' | cut -d '+' -f2)


echo "Current Version: $VERSION"
echo "Current Build Number: $BUILD_NUMBER"

UPDATE_FILE_NEEDED=0


# if $1 is 'b', then increase the build number by 1
# else if $1 is 'bv', then increase the version number by 1 and the build number by 1
if [ "$1" = "-bv" ]; then
  # Increment the version number
  VERSION=$(echo $VERSION | awk -F. -v OFS=. '{$NF++; print}')
  BUILD_NUMBER=$((BUILD_NUMBER + 1))
  UPDATE_FILE_NEEDED=1
  echo "Incremented Version: $VERSION"
  echo "Incremented Build Number: $BUILD_NUMBER"
elif [ "$1" = "-b" ]; then
  # Increment the build number
  BUILD_NUMBER=$((BUILD_NUMBER + 1))
  echo "Incremented Build Number: $BUILD_NUMBER"
  UPDATE_FILE_NEEDED=1
fi


# Ask for confirmation
read -p "Do you want to use these values? (y/n): " CONFIRM
if [ "$CONFIRM" != "y" ]; then
  read -p "Enter new version: " VERSION
  read -p "Enter new build number: " BUILD_NUMBER

  UPDATE_FILE_NEEDED=1
fi


if [ "$UPDATE_FILE_NEEDED" -eq 1 ]; then
  # Update pubspec.yaml with new values
  sed -i '' "s/^version: .*/version: $VERSION+$BUILD_NUMBER/" pubspec.yaml
  echo "Updated pubspec.yaml with version: $VERSION and build number: $BUILD_NUMBER"
  
  git commit -m "Update version to $VERSION+$BUILD_NUMBER" pubspec.yaml
  git push origin 
else
  echo "No changes made to version and build number."
fi





echo "Using Version: $VERSION"
echo "Using Build Number: $BUILD_NUMBER"

if ([ "$SHOULD_BUILD_IOS" -eq 1 ] ); then
  # Check if the iOS build is successful
  echo "Building iOS app..."

  dart run build_runner build 
  flutter pub  run easy_localization:generate -O lib/config/lang -f keys -o locale_keys.g.dart --source-dir ./assets/lang


  # Build the IPA
  flutter build ipa || ( echo "iOS build failed" && exit 1 )

  # upload the file 
  ./upload_ipa.sh

fi
#create tag : 
# Create tag
TAG="${VERSION}_${BUILD_NUMBER}"
echo "Tag created: $TAG"
git tag $TAG && git push origin $TAG


#TODO: handle Android build ##
# create the build branch by appending the version and build number to the default branch name
APK_BUILD_BRANCH="${APK_BUILD_BRANCH}_${VERSION}_${BUILD_NUMBER}"

# checkout auto_build branch
echo "Creating ${APK_BUILD_BRANCH} branch for APK build..."

git checkout -b ${APK_BUILD_BRANCH} && git merge --no-ff --no-message $TAG && git push origin ${APK_BUILD_BRANCH}




