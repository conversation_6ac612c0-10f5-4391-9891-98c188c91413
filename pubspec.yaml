name: family_app
description: "A new Flutter project."
publish_to: "none"
version: 0.0.13+54

environment:
  sdk: ">=3.3.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  collection: any
  cupertino_icons: ^1.0.2
  bloc: ^8.1.2
  cached_network_image: ^3.4.0
  flutter_bloc: ^8.1.3
  intl: ^0.19.0
  rxdart: ^0.27.7
  permission_handler: ^10.0.1
  json_annotation: ^4.9.0
  equatable: ^2.0.5
  flutter_svg: ^2.0.10+1
  get_it: ^7.6.0
  flutter_secure_storage: ^8.1.0
  shared_preferences: ^2.2.0
  path_provider: ^2.1.0
  path: ^1.8.2
  flutter_easyloading: ^3.0.5
  device_info_plus: ^9.0.2
  jiffy: ^6.2.1
  flutter_dotenv: ^5.2.1
  video_player: ^2.8.6
  image_picker: ^1.0.5
  file_picker: ^8.0.7
  url_launcher: ^6.1.14
  loadmore: ^2.1.0
  uuid: ^4.2.2
  easy_localization: ^3.0.7
  retrofit: ^4.1.0
  dio: 5.6.0
  injectable: ^2.4.2
  dartx: ^1.2.0
  auto_route: ^8.3.0
  vector_graphics: ^1.1.11+1
  path_drawing: ^1.0.1
  syncfusion_flutter_calendar: ^27.1.55
  web_socket:
    git:
      url: https://github.com/tuyen3962/http.git
      path: pkgs/web_socket/
  # record: ^5.1.2
  image: ^4.3.0
  firebase_core: ^3.4.0
  firebase_messaging: ^15.1.0
  flutter_local_notifications: ^18.0.1
  flutter_widget_from_html: ^0.15.3
  flutter_staggered_grid_view: ^0.4.0
  web_socket_channel: ^2.4.5
  logger: ^2.5.0
  timeago: ^3.7.0
  timeline_tile: ^2.0.0
  share_plus: ^10.0.0
  record: ^5.0.0
  flutter_audio_waveforms: ^1.2.1+8
  flutter_popup: ^3.3.4
  archive: ^4.0.2
  carousel_slider: ^5.0.0
  dotted_line: ^3.2.3
  dotted_border: ^2.1.0
  flutter_timezone: ^3.0.1
  chewie: 1.8.5
  in_app_purchase: ^3.2.1
  dropdown_search: ^6.0.2
  auto_size_text_plus: ^3.0.2
  smooth_page_indicator: ^1.2.1
  firebase_crashlytics: ^4.1.0
  firebase_analytics: ^11.3.0
  flutter_tts: ^4.2.2
  google_sign_in: ^6.3.0
  sign_in_with_apple: ^7.0.1
  device_calendar: ^4.3.3
  month_picker_dialog: ^6.0.3
  speech_to_text: ^6.6.0
  package_info_plus: ^6.0.0
  rfc_6902: ^0.3.0


dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0
  json_serializable: any
  build_runner: ^2.4.0
  retrofit_generator: 8.1.2
  #flutter_gen_runner: ^5.6.0
  flutter_gen_runner: 5.8.0
  auto_route_generator: ^8.1.0
  injectable_generator: ^2.6.2
  mockito: ^5.4.0

dependency_overrides:
  record_android: 1.2.6
  chewie: 1.8.0
  video_player_android: 2.7.16

flutter:
  uses-material-design: true
  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/icons/
    - assets/lang/
    - assets/images/
    - assets/demo_images/
    - assets/fonts/
    - .env

  fonts:
    - family: DM Sans
      fonts:
        - asset: assets/fonts/DMSans-Regular.ttf
          weight: 400
        - asset: assets/fonts/DMSans-Bold.ttf
          weight: 700

flutter_gen:
  integrations:
    flutter_svg: true
